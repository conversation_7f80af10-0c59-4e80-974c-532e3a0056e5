#!/bin/bash

# Migration script to split monorepo into separate repositories

echo "🚀 Starting migration to split repositories..."

# Create server repository
echo "📦 Creating server repository..."
mkdir -p saffron-saga-server
cd saffron-saga-server

# Copy server files
cp -r ../server/* .
cp ../server-only/* .
cp ../.nvmrc .
cp ../.npmrc .

# Initialize git for server
git init
echo "node_modules/" > .gitignore
echo "dist/" >> .gitignore
echo ".env" >> .gitignore
echo "*.log" >> .gitignore

echo "✅ Server repository created"

# Go back to root
cd ..

# Create client repository
echo "🌐 Creating client repository..."
mkdir -p saffron-saga-client
cd saffron-saga-client

# Copy client files
cp -r ../client/* .
cp ../client-only/* .

# Initialize git for client
git init
echo "node_modules/" > .gitignore
echo "dist/" >> .gitignore
echo ".env" >> .gitignore
echo ".env.local" >> .gitignore
echo "*.log" >> .gitignore

echo "✅ Client repository created"

# Go back to root
cd ..

echo "🎉 Migration complete!"
echo ""
echo "Next steps:"
echo "1. Create GitHub repositories:"
echo "   - saffron-saga-server"
echo "   - saffron-saga-client"
echo ""
echo "2. Push code to repositories:"
echo "   cd saffron-saga-server"
echo "   git remote add origin https://github.com/yourusername/saffron-saga-server.git"
echo "   git add ."
echo "   git commit -m 'Initial server setup'"
echo "   git push -u origin main"
echo ""
echo "   cd ../saffron-saga-client"
echo "   git remote add origin https://github.com/yourusername/saffron-saga-client.git"
echo "   git add ."
echo "   git commit -m 'Initial client setup'"
echo "   git push -u origin main"
echo ""
echo "3. Deploy:"
echo "   - Server: Deploy to Render using render.yaml"
echo "   - Client: Deploy to Vercel using vercel.json"
