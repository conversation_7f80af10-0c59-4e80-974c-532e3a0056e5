# 🚀 Manual Render Deployment Setup

## 🔧 **Backend Service Setup**

### **1. Create Web Service**
- **Type:** Web Service
- **Name:** `saffron-saga-server`
- **Environment:** Node
- **Region:** Ohio (or your preferred region)
- **Branch:** main

### **2. Build & Deploy Settings**
- **Root Directory:** `server`
- **Build Command:** `npm ci --only=production`
- **Start Command:** `npm start`

### **3. Environment Variables**
```env
NODE_ENV=production
PORT=8080
CORS_ORIGIN=https://saffron-saga-client.onrender.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SALT_ROUNDS=12
TRUST_PROXY=true
```

### **4. Auto-Generated Variables**
- **JWT_SECRET:** Auto-generate
- **SESSION_SECRET:** Auto-generate

### **5. Environment Group Variables**
Create group `saffron-saga-secrets`:
- `MONGODB_URI`
- `DB_NAME`
- `BREVO_EMAIL`
- `BREVO_USER`
- `BREVO_PASS`
- `RESTAURANT_EMAIL`

### **6. Advanced Settings**
- **Health Check Path:** `/health`
- **Auto-Deploy:** Yes

---

## 🌐 **Frontend Service Setup**

### **1. Create Static Site**
- **Type:** Static Site
- **Name:** `saffron-saga-client`
- **Environment:** Static Site
- **Region:** Ohio (or your preferred region)
- **Branch:** main

### **2. Build & Deploy Settings**
- **Root Directory:** `client`
- **Build Command:** `npm ci && npm run build`
- **Publish Directory:** `dist`

### **3. Environment Variables**
```env
NODE_ENV=production
VITE_API_BASE_URL=https://saffron-saga-server.onrender.com
```

### **4. Redirects & Rewrites**
Add this rewrite rule:
- **Source:** `/*`
- **Destination:** `/index.html`
- **Action:** Rewrite

---

## 🔍 **Troubleshooting**

### **If Render Uses Yarn Instead of NPM:**

1. **Delete the service** and recreate it
2. **Ensure no yarn.lock files** exist in your repo
3. **Use the exact commands** specified above
4. **Check the build logs** for `npm` vs `yarn` usage

### **Common Issues:**

1. **"Command start not found"**
   - Ensure Root Directory is set to `server`
   - Verify Start Command is `npm start`

2. **CORS Errors**
   - Update CORS_ORIGIN to match your frontend URL
   - Ensure both services are deployed

3. **Database Connection**
   - Verify MongoDB URI in environment group
   - Check IP whitelist settings

### **Manual Commands for Testing:**

```bash
# Test backend locally
cd server
npm ci --only=production
npm start

# Test frontend locally
cd client
npm ci
npm run build
```

---

## ✅ **Deployment Checklist**

### **Before Deployment:**
- [ ] Node.js version updated to 20.11.0
- [ ] No yarn.lock files in repository
- [ ] Package.json has correct start scripts
- [ ] Environment variables prepared

### **Backend Service:**
- [ ] Root Directory: `server`
- [ ] Build Command: `npm ci --only=production`
- [ ] Start Command: `npm start`
- [ ] Health Check: `/health`
- [ ] Environment variables set

### **Frontend Service:**
- [ ] Root Directory: `client`
- [ ] Build Command: `npm ci && npm run build`
- [ ] Publish Directory: `dist`
- [ ] Rewrite rule added
- [ ] Environment variables set

### **After Deployment:**
- [ ] Backend health check working
- [ ] Frontend loading correctly
- [ ] API calls successful
- [ ] Database connected
- [ ] Email service functional

---

**🎯 Use this guide if the Blueprint deployment continues to have issues!**
