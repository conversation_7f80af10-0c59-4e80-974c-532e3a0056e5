import dotenv from "dotenv";
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Make sure NODE_ENV is set before loading config
export const NODE_ENV = process.env.NODE_ENV || 'development';
export const IS_PRODUCTION = NODE_ENV === 'production';

// Load environment-specific config
const envFile = IS_PRODUCTION ? '.env.production' : '.env';
const envPath = resolve(__dirname, '../..', envFile);

// Load and validate environment variables
const envResult = dotenv.config({ path: envPath });
if (envResult.error) {
    throw new Error(`Environment configuration error: ${envResult.error.message}`);
}

// Load and validate critical environment variables
const requiredEnvVars = [
    'MONGODB_URI',
    'DB_NAME',
    'JWT_SECRET',
    'CORS_ORIGIN'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
}

// Export environment variables
export const PORT = parseInt(process.env.PORT || (IS_PRODUCTION ? '8080' : '4000'), 10);
export const MONGODB_URI = process.env.MONGODB_URI;
export const DB_NAME = process.env.DB_NAME;
export const CORS_ORIGIN = process.env.CORS_ORIGIN;
export const JWT_SECRET = process.env.JWT_SECRET;
export const SALT_ROUNDS = parseInt(process.env.SALT_ROUNDS || '10', 10);
export const BREVO_EMAIL = process.env.BREVO_EMAIL;
export const BREVO_USER = process.env.BREVO_USER;
export const BREVO_PASS = process.env.BREVO_PASS;
export const RESTAURANT_EMAIL = process.env.RESTAURANT_EMAIL;
export const RATE_LIMIT_WINDOW_MS = parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10);
export const RATE_LIMIT_MAX_REQUESTS = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10);
export const TRUST_PROXY = process.env.TRUST_PROXY === 'true';
