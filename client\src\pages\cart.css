@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

*,
*::before,
*::after {
  box-sizing: border-box;
}
* {
  margin: 0;
  padding: 0;
}
html,
body {
  height: 100%;
  width: 100%;
}
html {
  font-size: 62.5%;
}

.page-container {
  margin-inline: auto;
  width: 100%;
  max-width: 500px;
}
.heading {
  font-family: "Poppins", sans-serif;
  font-size: 3rem;
  line-height: 39px;
  font-weight: 400;
  font-style: normal;
}

:root {
  --font-c: #252525;
  --bg-c: #ffffff;
}

.cart-conatiner {
  width: 100%;
  height: 100dvh;
  background-color: #ffffff;
  overflow-y: scroll;
  padding-bottom: 14rem;
}
.back-btn {
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 22.4px;
  padding: 0.8rem;
  border: none;
  background-color: transparent;
}
.back-btn i {
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 22.4px;
  margin-right: 0.8rem;
}

.order-details {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 1.4rem;
  padding-inline: 1.6rem;
}
/* Counter number */
.counter span {
  font-size: 1rem;
  font-weight: 500;
  min-width: 25px;
  text-align: center;
}
.counter button {
  font-size: 1.8rem;
  font-weight: 400;
  padding: 0.8rem;
}

.detail-card {
  background-color: #ffffff;
  padding-block: 0.8rem;
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 1rem;
  align-items: end;
}
.card-top {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.card-top h4 {
  font-size: 1.8rem;
  font-weight: 400;
  line-height: 27px;
}
.card-top h3 {
  font-size: 1.8rem;
  font-weight: 600;
  line-height: 27px;
}

.next-btn {
  font-size: 1.4rem;
  line-height: 22.4px;
  font-weight: 600;
  font-family: inter;
  padding: 1.6rem 4rem;
  background-color: #ffdd00;
  border: none;
  border-radius: 0.8rem;
}
