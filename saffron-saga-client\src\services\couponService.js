import { apiClient, API_ENDPOINTS } from '../config/api.js';

class CouponService {
  // Validate and apply coupon
  async validateCoupon(couponCode, orderAmount, userId = null) {
    try {
      const response = await apiClient.post(API_ENDPOINTS.VALIDATE_COUPON, {
        code: couponCode,
        orderAmount,
        userId
      });

      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };
    } catch (error) {
      console.error('Validate coupon error:', error);
      
      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Failed to validate coupon',
          error: error.response.data
        };
      }
      
      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: error.message
      };
    }
  }

  // Create new coupon (Admin only)
  async createCoupon(couponData) {
    try {
      const response = await apiClient.post(API_ENDPOINTS.CREATE_COUPON, couponData);

      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };
    } catch (error) {
      console.error('Create coupon error:', error);
      
      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Failed to create coupon',
          errors: error.response.data.errors || [],
          error: error.response.data
        };
      }
      
      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: error.message
      };
    }
  }

  // Get all coupons (Admin only)
  async getCoupons(page = 1, limit = 10, status = 'all') {
    try {
      const response = await apiClient.get(API_ENDPOINTS.GET_COUPONS, {
        params: { page, limit, status }
      });

      return {
        success: true,
        data: response.data.data,
        pagination: response.data.pagination,
        message: 'Coupons fetched successfully'
      };
    } catch (error) {
      console.error('Get coupons error:', error);
      
      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Failed to fetch coupons',
          error: error.response.data
        };
      }
      
      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: error.message
      };
    }
  }

  // Update coupon status (Admin only)
  async updateCouponStatus(couponId, isActive) {
    try {
      const url = API_ENDPOINTS.UPDATE_COUPON_STATUS.replace(':id', couponId);
      const response = await apiClient.patch(url, { isActive });

      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };
    } catch (error) {
      console.error('Update coupon status error:', error);
      
      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Failed to update coupon status',
          error: error.response.data
        };
      }
      
      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: error.message
      };
    }
  }

  // Delete coupon (Admin only)
  async deleteCoupon(couponId) {
    try {
      const url = API_ENDPOINTS.DELETE_COUPON.replace(':id', couponId);
      const response = await apiClient.delete(url);

      return {
        success: true,
        message: response.data.message
      };
    } catch (error) {
      console.error('Delete coupon error:', error);
      
      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Failed to delete coupon',
          error: error.response.data
        };
      }
      
      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: error.message
      };
    }
  }

  // Helper method to format coupon display
  formatCouponDisplay(coupon) {
    const typeDisplay = coupon.type === 'percentage' 
      ? `${coupon.value}% OFF` 
      : `₹${coupon.value} OFF`;
    
    return {
      ...coupon,
      displayValue: typeDisplay,
      formattedMinOrder: coupon.minimumOrderAmount > 0 
        ? `Min order: ₹${coupon.minimumOrderAmount}` 
        : 'No minimum order',
      formattedMaxDiscount: coupon.maximumDiscountAmount 
        ? `Max discount: ₹${coupon.maximumDiscountAmount}` 
        : 'No maximum limit'
    };
  }

  // Helper method to calculate discount preview
  calculateDiscountPreview(coupon, orderAmount) {
    if (!coupon || orderAmount < coupon.minimumOrderAmount) {
      return {
        applicable: false,
        discount: 0,
        finalAmount: orderAmount,
        reason: orderAmount < coupon.minimumOrderAmount 
          ? `Minimum order amount is ₹${coupon.minimumOrderAmount}` 
          : 'Invalid coupon'
      };
    }

    let discount = 0;
    
    if (coupon.type === 'percentage') {
      discount = (orderAmount * coupon.value) / 100;
    } else if (coupon.type === 'fixed') {
      discount = coupon.value;
    }
    
    // Apply maximum discount limit if set
    if (coupon.maximumDiscountAmount && discount > coupon.maximumDiscountAmount) {
      discount = coupon.maximumDiscountAmount;
    }
    
    // Discount cannot exceed order amount
    discount = Math.min(discount, orderAmount);
    
    return {
      applicable: true,
      discount: discount,
      finalAmount: orderAmount - discount,
      savings: discount
    };
  }
}

// Create and export a singleton instance
const couponService = new CouponService();
export default couponService;
