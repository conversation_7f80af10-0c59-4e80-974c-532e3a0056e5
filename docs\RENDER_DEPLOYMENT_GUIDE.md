# Saffron Saga - Full Stack Deployment Guide for Render

## Overview

This guide provides step-by-step instructions for deploying the Saffron Saga full-stack application on Render. The application consists of:

- **Backend:** Node.js/Express API server
- **Frontend:** React/Vite client application  
- **Database:** MongoDB (external service)
- **Email:** Brevo email service integration

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Project Structure](#project-structure)
3. [Environment Setup](#environment-setup)
4. [Database Setup (MongoDB)](#database-setup-mongodb)
5. [Email Service Setup (Brevo)](#email-service-setup-brevo)
6. [Render Configuration](#render-configuration)
7. [Deployment Steps](#deployment-steps)
8. [Post-Deployment Verification](#post-deployment-verification)
9. [Troubleshooting](#troubleshooting)
10. [Monitoring and Maintenance](#monitoring-and-maintenance)

## Prerequisites

Before starting the deployment, ensure you have:

- [ ] GitHub repository with your code
- [ ] Render account (free tier available)
- [ ] MongoDB Atlas account (free tier available)
- [ ] Brevo account for email services (free tier available)
- [ ] Domain name (optional, Render provides free subdomains)

## Project Structure

```
saffron-saga/
├── client/                 # React frontend
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── vite.config.js
├── server/                 # Node.js backend
│   ├── src/
│   ├── package.json
│   └── .env.example
├── render.yaml            # Render configuration
├── package.json           # Root package.json
└── docs/                  # Documentation
```

## Environment Setup

### 1. Server Environment Variables

Create a `.env` file in the `server/` directory based on `.env.example`:

```bash
# Server Configuration
PORT=8080
NODE_ENV=production
MONGODB_URI=mongodb+srv://username:<EMAIL>/
DB_NAME=saffron_saga
CORS_ORIGIN=https://saffron-saga-client.onrender.com

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_here
SALT_ROUNDS=10
SESSION_SECRET=your_session_secret_here

# Email Configuration (Brevo)
BREVO_EMAIL=<EMAIL>
BREVO_USER=your-brevo-username
BREVO_PASS=your-brevo-smtp-password
RESTAURANT_EMAIL=<EMAIL>

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 2. Client Environment Variables

Create a `.env` file in the `client/` directory:

```bash
NODE_ENV=production
VITE_API_BASE_URL=https://saffron-saga-server.onrender.com
```

## Database Setup (MongoDB)

### 1. Create MongoDB Atlas Account

1. Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. Sign up for a free account
3. Create a new project named "Saffron Saga"

### 2. Create Database Cluster

1. Click "Create a Cluster"
2. Choose "M0 Sandbox" (free tier)
3. Select your preferred cloud provider and region
4. Name your cluster (e.g., "saffron-saga-cluster")
5. Click "Create Cluster"

### 3. Configure Database Access

1. Go to "Database Access" in the left sidebar
2. Click "Add New Database User"
3. Choose "Password" authentication
4. Create username and strong password
5. Set user privileges to "Read and write to any database"
6. Click "Add User"

### 4. Configure Network Access

1. Go to "Network Access" in the left sidebar
2. Click "Add IP Address"
3. Choose "Allow Access from Anywhere" (0.0.0.0/0)
4. Click "Confirm"

### 5. Get Connection String

1. Go to "Clusters" and click "Connect"
2. Choose "Connect your application"
3. Select "Node.js" and version "4.1 or later"
4. Copy the connection string
5. Replace `<password>` with your database user password
6. Replace `<dbname>` with `saffron_saga`

Example connection string:
```
mongodb+srv://username:<EMAIL>/saffron_saga?retryWrites=true&w=majority
```

## Email Service Setup (Brevo)

### 1. Create Brevo Account

1. Go to [Brevo](https://www.brevo.com/)
2. Sign up for a free account
3. Verify your email address

### 2. Get SMTP Credentials

1. Go to "SMTP & API" in the dashboard
2. Click on "SMTP" tab
3. Note down the SMTP settings:
   - **Server:** smtp-relay.brevo.com
   - **Port:** 587
   - **Username:** Your Brevo email
   - **Password:** Generate SMTP password

### 3. Configure Sender Email

1. Go to "Senders & IP"
2. Add and verify your sender email address
3. This will be used as the `RESTAURANT_EMAIL` in your environment variables

## Render Configuration

The project includes a `render.yaml` file that defines the deployment configuration:

```yaml
services:
  # Backend Service
  - type: web
    name: saffron-saga-server
    env: node
    plan: starter
    region: ohio
    buildCommand: cd server && npm install && npm run build
    startCommand: cd server && npm run start:prod
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 8080
      - key: CORS_ORIGIN
        value: https://saffron-saga-client.onrender.com
      # Other environment variables...

  # Frontend Service  
  - type: web
    name: saffron-saga-client
    env: node
    plan: starter
    region: ohio
    buildCommand: cd client && npm install && npm run build
    staticPublishPath: ./client/dist
    envVars:
      - key: NODE_ENV
        value: production
      - key: VITE_API_BASE_URL
        value: https://saffron-saga-server.onrender.com

# Environment Variable Groups
envVarGroups:
  - name: saffron-saga-secrets
    envVars:
      - key: BREVO_EMAIL
        sync: false
      - key: BREVO_USER  
        sync: false
      - key: BREVO_PASS
        sync: false
      - key: RESTAURANT_EMAIL
        sync: false
```

## Deployment Steps

### Step 1: Prepare Your Repository

1. **Ensure your code is pushed to GitHub:**
   ```bash
   git add .
   git commit -m "Prepare for Render deployment"
   git push origin main
   ```

2. **Verify the `render.yaml` file is in the root directory**

3. **Check that both `client/` and `server/` have their respective `package.json` files**

### Step 2: Create Render Account and Connect Repository

1. Go to [Render](https://render.com/) and sign up
2. Connect your GitHub account
3. Grant Render access to your repository

### Step 3: Create Environment Variable Group

1. In Render dashboard, go to "Environment Groups"
2. Click "New Environment Group"
3. Name it `saffron-saga-secrets`
4. Add the following variables:
   - `BREVO_EMAIL`: Your Brevo email
   - `BREVO_USER`: Your Brevo username  
   - `BREVO_PASS`: Your Brevo SMTP password
   - `RESTAURANT_EMAIL`: Your restaurant email
   - `MONGODB_URI`: Your MongoDB connection string
   - `DB_NAME`: `saffron_saga`
5. Click "Create Environment Group"

### Step 4: Deploy Using Blueprint

1. In Render dashboard, click "New +"
2. Select "Blueprint"
3. Connect your GitHub repository
4. Select the repository containing your `render.yaml`
5. Give your blueprint a name: "Saffron Saga"
6. Click "Apply"

### Step 5: Configure Additional Environment Variables

After the blueprint is applied, you may need to add additional environment variables:

1. Go to each service (server and client)
2. Navigate to "Environment" tab
3. Add any missing environment variables:
   - `JWT_SECRET`: Generate a secure random string
   - `SESSION_SECRET`: Generate another secure random string
   - `SALT_ROUNDS`: `10`

### Step 6: Monitor Deployment

1. Watch the build logs for both services
2. Server should build and start successfully
3. Client should build and deploy static files
4. Both services should show "Live" status

## Post-Deployment Verification

### 1. Health Check

Test the server health endpoint:
```bash
curl https://saffron-saga-server.onrender.com/health
```

Expected response:
```json
{
  "success": true,
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 60,
  "environment": "production"
}
```

### 2. API Endpoints

Test key API endpoints:

**Get Menu Items:**
```bash
curl https://saffron-saga-server.onrender.com/api/menu
```

**Test CORS (from browser console on your frontend):**
```javascript
fetch('https://saffron-saga-server.onrender.com/api/menu')
  .then(response => response.json())
  .then(data => console.log(data));
```

### 3. Frontend Application

1. Visit your frontend URL: `https://saffron-saga-client.onrender.com`
2. Verify the application loads correctly
3. Test menu item display
4. Test order creation functionality
5. Check browser console for any errors

### 4. Database Connection

Check if the database is properly connected by creating a test order or menu item through the admin interface.

### 5. Email Functionality

Test email sending by creating an order and verifying that the restaurant receives the order notification email.

## Troubleshooting

### Common Issues and Solutions

#### 1. Build Failures

**Issue:** Server build fails with "Module not found" errors
**Solution:**
- Ensure all dependencies are listed in `server/package.json`
- Check that the build command in `render.yaml` is correct
- Verify Node.js version compatibility

**Issue:** Client build fails during Vite build
**Solution:**
- Check `client/package.json` for missing dependencies
- Ensure environment variables are properly set
- Verify Vite configuration in `vite.config.js`

#### 2. Database Connection Issues

**Issue:** "MongoNetworkError" or connection timeouts
**Solution:**
- Verify MongoDB connection string is correct
- Check that IP whitelist includes 0.0.0.0/0 in MongoDB Atlas
- Ensure database user has proper permissions
- Verify the database name matches your configuration

**Issue:** "Authentication failed" errors
**Solution:**
- Double-check MongoDB username and password
- Ensure special characters in password are URL-encoded
- Verify the database user exists and has correct permissions

#### 3. CORS Issues

**Issue:** Frontend can't connect to backend API
**Solution:**
- Verify `CORS_ORIGIN` environment variable matches your frontend URL
- Check that both services are deployed and running
- Ensure API calls use the correct backend URL

#### 4. Environment Variable Issues

**Issue:** Environment variables not loading
**Solution:**
- Check that variables are set in Render dashboard
- Verify environment group is properly linked
- Ensure variable names match exactly (case-sensitive)
- Restart services after changing environment variables

#### 5. Email Service Issues

**Issue:** Order emails not being sent
**Solution:**
- Verify Brevo SMTP credentials are correct
- Check that sender email is verified in Brevo
- Ensure `RESTAURANT_EMAIL` is set correctly
- Check server logs for email-related errors

### Debugging Steps

1. **Check Service Logs:**
   - Go to your service in Render dashboard
   - Click on "Logs" tab
   - Look for error messages and stack traces

2. **Test API Endpoints Individually:**
   ```bash
   # Test health check
   curl https://your-server.onrender.com/health

   # Test menu endpoint
   curl https://your-server.onrender.com/api/menu
   ```

3. **Verify Environment Variables:**
   - In Render dashboard, go to service → Environment
   - Ensure all required variables are present
   - Check for typos in variable names

4. **Database Connection Test:**
   ```bash
   # Use health check with validation
   curl "https://your-server.onrender.com/health?validate=true"
   ```

## Monitoring and Maintenance

### 1. Service Monitoring

**Render Dashboard:**
- Monitor service status and uptime
- Check resource usage (CPU, memory)
- Review deployment history
- Set up alerts for service failures

**Health Checks:**
- Render automatically monitors the `/health` endpoint
- Configure custom health check timeout if needed
- Monitor response times and error rates

### 2. Database Monitoring

**MongoDB Atlas:**
- Monitor database performance metrics
- Set up alerts for connection issues
- Review query performance
- Monitor storage usage

### 3. Log Management

**Server Logs:**
- Regularly review application logs in Render
- Monitor for error patterns
- Set up log retention policies

**Error Tracking:**
- Consider integrating error tracking services (Sentry, LogRocket)
- Monitor client-side errors
- Track API error rates

### 4. Performance Optimization

**Backend Optimization:**
- Monitor API response times
- Optimize database queries
- Implement caching where appropriate
- Consider upgrading Render plan for better performance

**Frontend Optimization:**
- Monitor bundle size and loading times
- Optimize images and assets
- Implement lazy loading
- Use CDN for static assets

### 5. Security Maintenance

**Regular Updates:**
- Keep dependencies updated
- Monitor security advisories
- Update Node.js version as needed

**Environment Security:**
- Rotate JWT secrets periodically
- Monitor for suspicious activity
- Review access logs

### 6. Backup Strategy

**Database Backups:**
- MongoDB Atlas provides automatic backups
- Consider additional backup strategies for critical data
- Test backup restoration procedures

**Code Backups:**
- Ensure code is backed up in version control
- Tag releases for easy rollback
- Maintain deployment documentation

## Scaling Considerations

### 1. Horizontal Scaling

**Render Scaling:**
- Upgrade to paid plans for auto-scaling
- Configure multiple instances for high availability
- Use load balancing for better performance

### 2. Database Scaling

**MongoDB Scaling:**
- Monitor database performance metrics
- Consider upgrading to dedicated clusters
- Implement read replicas for better performance

### 3. CDN and Caching

**Static Asset Optimization:**
- Use Render's built-in CDN for static files
- Implement browser caching strategies
- Consider external CDN services for global distribution

## Cost Optimization

### 1. Free Tier Limitations

**Render Free Tier:**
- Services sleep after 15 minutes of inactivity
- 750 hours per month across all services
- Limited bandwidth and storage

**MongoDB Atlas Free Tier:**
- 512 MB storage limit
- Shared cluster performance
- Limited to one free cluster per project

### 2. Upgrade Recommendations

**When to Upgrade:**
- Consistent traffic requiring always-on services
- Need for better performance and reliability
- Storage requirements exceed free tier limits
- Need for advanced features (custom domains, etc.)

## Support and Resources

### Documentation Links
- [Render Documentation](https://render.com/docs)
- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com/)
- [Brevo API Documentation](https://developers.brevo.com/)

### Community Support
- Render Community Forum
- MongoDB Community Forums
- Stack Overflow for technical questions

### Professional Support
- Consider paid support plans for production applications
- Render offers priority support for paid plans
- MongoDB Atlas provides technical support options

---

## Quick Reference

### Important URLs
- **Frontend:** `https://saffron-saga-client.onrender.com`
- **Backend:** `https://saffron-saga-server.onrender.com`
- **API Health:** `https://saffron-saga-server.onrender.com/health`

### Key Commands
```bash
# Test API health
curl https://saffron-saga-server.onrender.com/health

# Test menu endpoint
curl https://saffron-saga-server.onrender.com/api/menu

# Check detailed validation
curl "https://saffron-saga-server.onrender.com/health?validate=true"
```

### Emergency Contacts
- Render Status Page: https://status.render.com/
- MongoDB Atlas Status: https://status.cloud.mongodb.com/
- Brevo Status: https://status.brevo.com/

This completes the comprehensive deployment guide for the Saffron Saga application on Render.
