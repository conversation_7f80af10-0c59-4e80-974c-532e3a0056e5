<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>CORS Test for Saffron Saga</h1>
    <button onclick="testCORS()">Test CORS</button>
    <div id="result"></div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing CORS...';
            
            try {
                const response = await fetch('http://localhost:8080/health', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.text();
                    resultDiv.innerHTML = `<p style="color: green;">✅ CORS Success!</p><pre>${data}</pre>`;
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">❌ Response Error: ${response.status}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">❌ CORS Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
