# 🍛 Saffron Saga Client

Frontend application for the Saffron Saga Restaurant.

## 🚀 Quick Start

### Prerequisites
- Node.js 20.11.0 or higher
- Backend API running (see saffron-saga-server repository)

### Local Development
```bash
# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Update .env with your backend API URL

# Start development server
npm run dev
```

The application will be available at `http://localhost:3000`

### Production Deployment (Vercel)

1. **Create new repository** for client-only code
2. **Push this client code** to the new repository
3. **Deploy to Vercel** using the included vercel.json

#### Environment Variables Required:
- `VITE_API_BASE_URL` - Backend API URL (e.g., https://your-server.onrender.com)

## 🔧 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:prod` - Build for production with optimizations
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint errors
- `npm run clean` - Clean build directory

## 🌐 API Configuration

Update `VITE_API_BASE_URL` in your environment:
- Development: `http://localhost:8080`
- Production: `https://your-backend.onrender.com`

## 📱 Features

- **Menu Display** - Browse restaurant menu items
- **Order Placement** - Place orders with customer details
- **Admin Panel** - Manage menu items and orders
- **Responsive Design** - Works on desktop and mobile
- **Real-time Updates** - Dynamic menu availability

## 🎨 Tech Stack

- **React 19** - Frontend framework
- **Vite** - Build tool and dev server
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **React Toastify** - Notifications
- **Yup** - Form validation

## 📝 License

ISC License
