import { Router } from "express";
import {
  createMenuItemController,
  editMenuItemAvailability,
  getAllMenuItemController,
} from "../controllers/menuItem.controller.js";
import { menuItemValidation } from "../validators/menuItem.validator.js";
import { authorizeAdmin } from "../middlewares/auth.middleware.js";
const router = Router();

// get list of menu item
router.get("/", getAllMenuItemController);

// create menu item
router.post(
  "/create",
  authorizeAdmin,
  menuItemValidation,
  createMenuItemController
);


// edit item isAvailable
router.post("/edit-avilability", authorizeAdmin, editMenuItemAvailability);

export default router;
