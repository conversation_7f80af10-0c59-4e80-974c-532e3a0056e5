/**
 * Image optimization utilities for better performance
 */

/**
 * Generate optimized image URL with query parameters
 * @param {string} url - Original image URL
 * @param {Object} options - Optimization options
 * @returns {string} - Optimized image URL
 */
export const getOptimizedImageUrl = (url, options = {}) => {
  if (!url) return null;
  
  const {
    width,
    height,
    quality = 80,
    format = 'webp',
    fit = 'cover'
  } = options;

  // If it's a local asset, return as is
  if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
    return url;
  }

  // For external URLs, you could integrate with image optimization services
  // like Cloudinary, ImageKit, or similar services
  // For now, return the original URL
  return url;
};

/**
 * Create responsive image srcSet for different screen sizes
 * @param {string} baseUrl - Base image URL
 * @param {Array} sizes - Array of sizes [width, height]
 * @returns {string} - srcSet string
 */
export const createResponsiveSrcSet = (baseUrl, sizes = []) => {
  if (!baseUrl || !sizes.length) return '';

  return sizes
    .map(([width, height]) => {
      const optimizedUrl = getOptimizedImageUrl(baseUrl, { width, height });
      return `${optimizedUrl} ${width}w`;
    })
    .join(', ');
};

/**
 * Preload critical images
 * @param {Array} imageUrls - Array of image URLs to preload
 * @returns {Promise} - Promise that resolves when all images are loaded
 */
export const preloadImages = (imageUrls) => {
  const promises = imageUrls.map((url) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(url);
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      img.src = url;
    });
  });

  return Promise.allSettled(promises);
};

/**
 * Check if WebP format is supported
 * @returns {boolean} - True if WebP is supported
 */
export const isWebPSupported = () => {
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
};

/**
 * Get the best image format for the browser
 * @returns {string} - Best supported format
 */
export const getBestImageFormat = () => {
  if (isWebPSupported()) return 'webp';
  return 'jpg';
};

/**
 * Calculate optimal image dimensions based on container and device pixel ratio
 * @param {Object} container - Container dimensions {width, height}
 * @param {number} devicePixelRatio - Device pixel ratio
 * @returns {Object} - Optimal dimensions {width, height}
 */
export const calculateOptimalDimensions = (container, devicePixelRatio = window.devicePixelRatio || 1) => {
  const { width, height } = container;
  
  return {
    width: Math.ceil(width * devicePixelRatio),
    height: Math.ceil(height * devicePixelRatio)
  };
};

/**
 * Lazy load images with intersection observer
 * @param {string} selector - CSS selector for images to lazy load
 * @param {Object} options - Intersection observer options
 */
export const initLazyLoading = (selector = 'img[data-src]', options = {}) => {
  const defaultOptions = {
    rootMargin: '100px',
    threshold: 0.1,
    ...options
  };

  const images = document.querySelectorAll(selector);
  
  if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target;
          const src = img.getAttribute('data-src');
          
          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
            observer.unobserve(img);
          }
        }
      });
    }, defaultOptions);

    images.forEach((img) => observer.observe(img));
    
    return observer;
  } else {
    // Fallback for browsers without IntersectionObserver
    images.forEach((img) => {
      const src = img.getAttribute('data-src');
      if (src) {
        img.src = src;
        img.removeAttribute('data-src');
        img.classList.add('loaded');
      }
    });
  }
};

/**
 * Image loading performance metrics
 */
export class ImagePerformanceTracker {
  constructor() {
    this.metrics = {
      totalImages: 0,
      loadedImages: 0,
      failedImages: 0,
      averageLoadTime: 0,
      loadTimes: []
    };
  }

  startTracking(imageUrl) {
    const startTime = performance.now();
    this.metrics.totalImages++;

    return {
      onLoad: () => {
        const loadTime = performance.now() - startTime;
        this.metrics.loadedImages++;
        this.metrics.loadTimes.push(loadTime);
        this.updateAverageLoadTime();
        
        console.log(`Image loaded: ${imageUrl} in ${loadTime.toFixed(2)}ms`);
      },
      onError: () => {
        this.metrics.failedImages++;
        console.warn(`Image failed to load: ${imageUrl}`);
      }
    };
  }

  updateAverageLoadTime() {
    const { loadTimes } = this.metrics;
    if (loadTimes.length > 0) {
      this.metrics.averageLoadTime = loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length;
    }
  }

  getMetrics() {
    return {
      ...this.metrics,
      successRate: this.metrics.totalImages > 0 
        ? (this.metrics.loadedImages / this.metrics.totalImages) * 100 
        : 0
    };
  }

  reset() {
    this.metrics = {
      totalImages: 0,
      loadedImages: 0,
      failedImages: 0,
      averageLoadTime: 0,
      loadTimes: []
    };
  }
}

// Global performance tracker instance
export const imagePerformanceTracker = new ImagePerformanceTracker();

/**
 * Optimize image loading based on connection speed
 * @returns {Object} - Optimization settings
 */
export const getConnectionBasedOptimization = () => {
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
  
  if (!connection) {
    return { quality: 80, format: 'webp' };
  }

  const { effectiveType, downlink } = connection;
  
  // Adjust quality based on connection speed
  if (effectiveType === 'slow-2g' || effectiveType === '2g') {
    return { quality: 50, format: 'jpg' };
  } else if (effectiveType === '3g') {
    return { quality: 65, format: 'webp' };
  } else if (effectiveType === '4g' || downlink > 10) {
    return { quality: 85, format: 'webp' };
  }
  
  return { quality: 80, format: 'webp' };
};

export default {
  getOptimizedImageUrl,
  createResponsiveSrcSet,
  preloadImages,
  isWebPSupported,
  getBestImageFormat,
  calculateOptimalDimensions,
  initLazyLoading,
  ImagePerformanceTracker,
  imagePerformanceTracker,
  getConnectionBasedOptimization
};
