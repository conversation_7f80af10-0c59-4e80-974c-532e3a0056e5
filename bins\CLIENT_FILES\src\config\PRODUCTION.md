# Saffron Saga - Production Deployment Guide

## 🚀 Complete Deployment on Render

This application is fully optimized with lazy loading, performance enhancements, and production-ready configuration for deployment on Render.

### ✨ Application Features
- 🍛 Restaurant menu browsing with lazy-loaded images
- 🛒 Shopping cart functionality
- 📝 Order placement with email notifications
- 🔐 Admin authentication and menu management
- ⚡ Performance optimized with lazy loading
- 📱 Mobile-responsive design
- 🔒 Security features and rate limiting

### Prerequisites
- GitHub repository with your code
- [Render account](https://render.com) (free tier available)
- [MongoDB Atlas account](https://www.mongodb.com/cloud/atlas) (free tier available)
- [Brevo account](https://www.brevo.com) for email services (free tier available)

## 📋 Step-by-Step Deployment

### Step 1: Prepare External Services

#### MongoDB Atlas Setup
1. **Create Account & Cluster**
   - Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
   - Sign up for free account
   - Create new project: "Saffron Saga"
   - Create M0 Sandbox cluster (free tier)

2. **Configure Database Access**
   - Go to "Database Access" → "Add New Database User"
   - Create username and strong password
   - Set privileges: "Read and write to any database"

3. **Configure Network Access**
   - Go to "Network Access" → "Add IP Address"
   - Choose "Allow Access from Anywhere" (0.0.0.0/0)

4. **Get Connection String**
   - Go to "Clusters" → "Connect" → "Connect your application"
   - Copy connection string
   - Replace `<password>` and set database name to `saffron_saga`
   - Example: `mongodb+srv://username:<EMAIL>/saffron_saga`

#### Brevo Email Service Setup
1. **Create Account**
   - Go to [Brevo](https://www.brevo.com/)
   - Sign up for free account
   - Verify email address

2. **Get SMTP Credentials**
   - Go to "SMTP & API" → "SMTP" tab
   - Note SMTP settings:
     - Server: smtp-relay.brevo.com
     - Port: 587
     - Username: Your Brevo email
     - Password: Generate SMTP password

3. **Verify Sender Email**
   - Go to "Senders & IP" → Add sender email
   - This will be your `RESTAURANT_EMAIL`

### Step 2: Deploy on Render

#### Option A: Blueprint Deployment (Recommended)

1. **Prepare Repository**
   ```bash
   # Ensure all changes are committed
   git add .
   git commit -m "Production ready with lazy loading optimization"
   git push origin main
   ```

2. **Create Environment Variable Group**
   - Go to Render dashboard
   - Navigate to "Environment Groups"
   - Click "New Environment Group"
   - Name: `saffron-saga-secrets`
   - Add variables:
     ```env
     MONGODB_URI=mongodb+srv://username:<EMAIL>/saffron_saga
     DB_NAME=saffron_saga
     BREVO_EMAIL=<EMAIL>
     BREVO_USER=your-brevo-username
     BREVO_PASS=your-brevo-smtp-password
     RESTAURANT_EMAIL=<EMAIL>
     ```

3. **Deploy with Blueprint**
   - Click "New +" → "Blueprint"
   - Connect your GitHub repository
   - Select the repository containing `render.yaml`
   - Give blueprint a name: "Saffron Saga"
   - Click "Apply"

4. **Monitor Deployment**
   - Watch build logs for both services
   - Ensure both show "Live" status
   - Backend: `saffron-saga-server`
   - Frontend: `saffron-saga-client`

### Step 3: Verify Deployment

#### Health Check Verification
```bash
# Basic health check
curl https://saffron-saga-server.onrender.com/health

# Expected response:
{
  "success": true,
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 60,
  "environment": "production"
}
```

#### API Endpoints Testing
```bash
# Test menu endpoint
curl https://saffron-saga-server.onrender.com/api/menu

# Test CORS (from browser console)
fetch('https://saffron-saga-server.onrender.com/api/menu')
  .then(response => response.json())
  .then(data => console.log(data));
```

#### Frontend Verification
1. **Visit Application**: `https://saffron-saga-client.onrender.com`
2. **Test Features**:
   - ✅ Menu items load with lazy loading
   - ✅ Images load progressively as you scroll
   - ✅ Cart functionality works
   - ✅ Order placement works
   - ✅ Email notifications are sent
   - ✅ Mobile responsiveness

#### Option B: Manual Service Creation

If you prefer manual setup:

1. **Create Backend Service**
   - Click "New +" → "Web Service"
   - Connect GitHub repository
   - Configure:
     ```
     Name: saffron-saga-server
     Environment: Node
     Build Command: cd server && npm install && npm run build
     Start Command: cd server && npm run start:prod
     Health Check Path: /health
     ```

2. **Create Frontend Service**
   - Click "New +" → "Static Site"
   - Connect same repository
   - Configure:
     ```
     Name: saffron-saga-client
     Build Command: cd client && npm install && npm run build
     Publish Directory: client/dist
     ```

## 🔧 Environment Variables Configuration

### Backend Service Variables
```env
NODE_ENV=production
PORT=4000
MONGODB_URI=your_mongodb_connection_string
DB_NAME=saffron_saga
JWT_SECRET=auto-generated-by-render
SESSION_SECRET=auto-generated-by-render
CORS_ORIGIN=https://saffron-saga-client.onrender.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SALT_ROUNDS=10
# From environment group:
BREVO_EMAIL=<EMAIL>
BREVO_USER=your-brevo-username
BREVO_PASS=your-brevo-password
RESTAURANT_EMAIL=<EMAIL>
```

### Frontend Service Variables
```env
NODE_ENV=production
VITE_API_BASE_URL=https://saffron-saga-server.onrender.com
```

## 📊 Production Features

### 🔒 Security Features
- JWT authentication with HTTP-only cookies
- Password hashing with bcrypt (10 salt rounds)
- CORS protection with configurable origins
- Rate limiting (100 requests per 15 minutes)
- Input validation and sanitization
- Security headers with Helmet.js
- Environment variable protection

### ⚡ Performance Features
- **Lazy Loading**: Images load progressively as user scrolls
- **Connection-Aware Quality**: Adjusts image quality based on connection speed
- **Static Asset Caching**: 1 year for assets, no-cache for HTML
- **Gzip Compression**: Automatic compression for all assets
- **Code Splitting**: Vendor and app bundles separated
- **CDN Delivery**: Global content delivery through Render
- **Optimized Database Queries**: Efficient MongoDB operations

### 📈 Monitoring & Health
- **Health Check Endpoint**: `/health` with detailed validation
- **Performance Metrics**: Built-in image loading performance tracking
- **Application Logs**: Available in Render dashboard
- **Automatic Restart**: Service restart on failures
- **Uptime Monitoring**: Built-in health check monitoring

## 🛠️ Local Development

### Prerequisites
```bash
# Check Node.js version
node --version  # Should be >=18.0.0

# Check npm version
npm --version
```

### Setup Development Environment
```bash
# Clone repository
git clone https://github.com/your-username/saffron-saga.git
cd saffron-saga

# Install dependencies
cd client && npm install
cd ../server && npm install
cd ..

# Set up environment variables
cp server/.env.example server/.env
cp client/.env.example client/.env

# Edit environment files with your local configuration
```

### Start Development Servers
```bash
# Start backend (from root directory)
npm run dev:server  # Runs on http://localhost:4000

# Start frontend (in new terminal, from root directory)
npm run dev:client  # Runs on http://localhost:5173
```

### Build for Production
```bash
# Build frontend
npm run build:client

# Build backend (no build step needed for Node.js)
npm run build:server

# Test production build locally
npm run start:prod
```

## 📚 Complete Documentation

- **[📋 Production Quick Start](PRODUCTION.md)** - This guide
- **[🔌 Complete API Documentation](docs/API_DOCUMENTATION.md)** - All endpoints and schemas
- **[🚀 Detailed Render Deployment Guide](docs/RENDER_DEPLOYMENT_GUIDE.md)** - Comprehensive deployment
- **[⚡ Lazy Loading Implementation Guide](docs/LAZY_LOADING_GUIDE.md)** - Performance optimization
- **[📖 Production Ready Summary](PRODUCTION_READY.md)** - What was optimized

## 🆘 Troubleshooting Guide

### Build Issues

**Frontend Build Failures:**
```bash
# Clear cache and reinstall
cd client
rm -rf node_modules package-lock.json dist
npm install
npm run build
```

**Backend Build Issues:**
```bash
# Check syntax
cd server
node --check src/server.js
node --check src/app.js
```

### Database Connection Issues

**MongoDB Connection Errors:**
1. Verify connection string format
2. Check IP whitelist includes 0.0.0.0/0
3. Ensure database user has correct permissions
4. Test connection locally first

**Connection String Format:**
```
mongodb+srv://username:<EMAIL>/saffron_saga?retryWrites=true&w=majority
```

### API Issues

**CORS Errors:**
- Verify `CORS_ORIGIN` exactly matches frontend URL
- Check both services are deployed and running
- Test API endpoints directly

**Authentication Issues:**
- Verify JWT_SECRET is set
- Check cookie settings in production
- Ensure HTTPS is used for secure cookies

### Email Service Issues

**Brevo SMTP Errors:**
1. Verify SMTP credentials are correct
2. Check sender email is verified in Brevo
3. Review server logs for detailed error messages
4. Test SMTP connection separately

### Performance Issues

**Slow Loading:**
- Check lazy loading is working correctly
- Verify image optimization is active
- Monitor network tab in browser dev tools
- Check Render service logs for performance metrics

**Memory Issues:**
- Monitor memory usage in Render dashboard
- Check for memory leaks in application logs
- Consider upgrading Render plan if needed

## 🔄 Deployment Maintenance

### Automatic Deployments
- **Trigger**: Pushes to `main` branch
- **Process**: Both frontend and backend update simultaneously
- **Downtime**: Zero-downtime deployments
- **Rollback**: Available through Render dashboard

### Manual Deployment
```bash
# Make changes
git add .
git commit -m "Your changes"
git push origin main

# Monitor in Render dashboard
# Check deployment logs
# Verify health endpoints
```

### Health Monitoring
```bash
# Basic health check
curl https://saffron-saga-server.onrender.com/health

# Detailed validation
curl "https://saffron-saga-server.onrender.com/health?validate=true"

# Monitor performance
curl https://saffron-saga-server.onrender.com/api/menu
```

### Performance Monitoring
- **Render Dashboard**: Monitor CPU, memory, and response times
- **Application Logs**: Review for errors and performance issues
- **Health Endpoints**: Regular automated checks
- **User Feedback**: Monitor for performance complaints

## 🎯 Production URLs

Once deployed, your application will be available at:

- **🌐 Frontend Application**: `https://saffron-saga-client.onrender.com`
- **🔌 Backend API**: `https://saffron-saga-server.onrender.com`
- **💚 Health Check**: `https://saffron-saga-server.onrender.com/health`
- **📋 API Menu**: `https://saffron-saga-server.onrender.com/api/menu`

## 🎉 Success Metrics

After successful deployment, you should see:

### Performance Metrics
- **Initial Load Time**: 2-3 seconds
- **Time to Interactive**: 3-4 seconds
- **Largest Contentful Paint**: 2-3 seconds
- **First Input Delay**: <100ms
- **Cumulative Layout Shift**: <0.1

### Functionality Verification
- ✅ Menu items load with lazy loading
- ✅ Images load progressively
- ✅ Cart functionality works
- ✅ Order placement successful
- ✅ Email notifications sent
- ✅ Admin authentication works
- ✅ Mobile responsive design
- ✅ API endpoints respond correctly

### Security Verification
- ✅ HTTPS enabled
- ✅ CORS configured correctly
- ✅ Rate limiting active
- ✅ Authentication working
- ✅ Environment variables secure

**🎊 Congratulations! Your Saffron Saga restaurant application is now live and serving customers with optimal performance!**
