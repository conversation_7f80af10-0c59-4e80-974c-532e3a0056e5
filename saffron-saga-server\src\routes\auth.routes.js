import { Router } from "express";
import {
  loginAdminController,
  logoutAdminController,
  registerAdminController,
} from "../controllers/auth.controller.js";
const router = Router();
import {
  loginValidation,
  registerValidation,
} from "../validators/auth.validator.js";
import { authorizeAdmin } from "../middlewares/auth.middleware.js";

// Admin Auth Routes
// Register new admin
router.post("/admin/register", registerValidation, registerAdminController);

// Login Admin
router.post("/admin/login", loginValidation, loginAdminController);

// Logout  Admin
router.post("/admin/logout", authorizeAdmin, logoutAdminController);

// // User Auth Routes
// // Register and Login User
// router.post("/users/get-start", getStartValidation, getStartUserController);

// // Logout User
// router.post("/users/Logout", authorizeUser, logoutUserController);
// // Logout User
// router.get("/users/is-auth", authorizeUser, checkUserAuthController);

export default router;
