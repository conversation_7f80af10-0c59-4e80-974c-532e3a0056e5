import { useState, useRef, useEffect, useCallback } from 'react';

/**
 * Custom hook for lazy loading images with intersection observer
 * @param {Object} options - Configuration options
 * @param {string} options.rootMargin - Root margin for intersection observer
 * @param {number} options.threshold - Threshold for intersection observer
 * @param {boolean} options.triggerOnce - Whether to trigger only once
 * @returns {Object} - Hook state and refs
 */
export const useLazyLoading = (options = {}) => {
  const {
    rootMargin = '100px',
    threshold = 0.1,
    triggerOnce = true
  } = options;

  const [isInView, setIsInView] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const elementRef = useRef(null);
  const observerRef = useRef(null);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
    setIsLoaded(true); // Consider error as "loaded" to stop loading state
  }, []);

  const resetState = useCallback(() => {
    setIsInView(false);
    setIsLoaded(false);
    setHasError(false);
  }, []);

  useEffect(() => {
    const currentElement = elementRef.current;
    
    if (!currentElement) return;

    // Create intersection observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            
            // Stop observing if triggerOnce is true
            if (triggerOnce && observerRef.current) {
              observerRef.current.unobserve(currentElement);
            }
          } else if (!triggerOnce) {
            setIsInView(false);
          }
        });
      },
      {
        rootMargin,
        threshold
      }
    );

    // Start observing
    observerRef.current.observe(currentElement);

    // Cleanup function
    return () => {
      if (observerRef.current && currentElement) {
        observerRef.current.unobserve(currentElement);
      }
    };
  }, [rootMargin, threshold, triggerOnce]);

  // Cleanup observer on unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  return {
    elementRef,
    isInView,
    isLoaded,
    hasError,
    handleLoad,
    handleError,
    resetState
  };
};

/**
 * Hook specifically for image lazy loading
 * @param {string} src - Image source URL
 * @param {string} fallbackSrc - Fallback image source
 * @param {Object} options - Lazy loading options
 * @returns {Object} - Image loading state and handlers
 */
export const useImageLazyLoading = (src, fallbackSrc, options = {}) => {
  const {
    elementRef,
    isInView,
    isLoaded,
    hasError,
    handleLoad,
    handleError,
    resetState
  } = useLazyLoading(options);

  const [imageSrc, setImageSrc] = useState(null);

  // Update image source when in view
  useEffect(() => {
    if (isInView && src) {
      setImageSrc(hasError ? fallbackSrc : src);
    }
  }, [isInView, src, fallbackSrc, hasError]);

  // Reset state when src changes
  useEffect(() => {
    resetState();
    setImageSrc(null);
  }, [src, resetState]);

  return {
    elementRef,
    isInView,
    isLoaded,
    hasError,
    imageSrc,
    handleLoad,
    handleError,
    shouldLoad: isInView && imageSrc
  };
};

/**
 * Hook for preloading images
 * @param {Array} imageUrls - Array of image URLs to preload
 * @returns {Object} - Preloading state
 */
export const useImagePreloader = (imageUrls = []) => {
  const [loadedImages, setLoadedImages] = useState(new Set());
  const [failedImages, setFailedImages] = useState(new Set());
  const [isPreloading, setIsPreloading] = useState(false);

  const preloadImages = useCallback(async () => {
    if (!imageUrls.length) return;

    setIsPreloading(true);
    const loadPromises = imageUrls.map((url) => {
      return new Promise((resolve) => {
        const img = new Image();
        
        img.onload = () => {
          setLoadedImages(prev => new Set([...prev, url]));
          resolve({ url, success: true });
        };
        
        img.onerror = () => {
          setFailedImages(prev => new Set([...prev, url]));
          resolve({ url, success: false });
        };
        
        img.src = url;
      });
    });

    await Promise.all(loadPromises);
    setIsPreloading(false);
  }, [imageUrls]);

  useEffect(() => {
    preloadImages();
  }, [preloadImages]);

  return {
    loadedImages,
    failedImages,
    isPreloading,
    preloadImages
  };
};

export default useLazyLoading;
