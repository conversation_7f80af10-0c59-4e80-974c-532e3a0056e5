import express from "express";
import cors from "cors";
import cookieParser from "cookie-parser";
import helmet from "helmet";
import compression from "compression";
import rateLimit from "express-rate-limit";
import morgan from "morgan";
import GlobelError from "./middlewares/error.middleware.js";
import {
  CORS_ORIGIN,
  IS_PRODUCTION,
  RATE_LIMIT_WINDOW_MS,
  RATE_LIMIT_MAX_REQUESTS,
  TRUST_PROXY,
  NODE_ENV
} from "./config/env.js";

// Routers
import healthRoutes from "./routes/health.routes.js";
import authRouters from "./routes/auth.routes.js";
import menuItemRouters from "./routes/menuItem.route.js";
import orderRouters from "./routes/order.routes.js";

const app = express();

// Trust proxy in production (for rate limiting, etc.)
if (IS_PRODUCTION && TRUST_PROXY) {
  app.set('trust proxy', 1);
}

// Health check endpoint - before any middleware
app.use('/health', healthRoutes);

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: IS_PRODUCTION ? undefined : false
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: RATE_LIMIT_WINDOW_MS || 15 * 60 * 1000,
  max: RATE_LIMIT_MAX_REQUESTS || 100
});

app.use(limiter);

// Compression
app.use(compression());

// Logging
if (IS_PRODUCTION) {
  app.use(morgan('combined'));
} else {
  app.use(morgan('dev'));
}

// Parse CORS_ORIGIN for multiple origins
const allowedOrigins = CORS_ORIGIN ? CORS_ORIGIN.split(',').map(origin => origin.trim()) : ['http://localhost:5173'];

// CORS configuration
const corsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps, curl, Postman)
    if (!origin) {
      return callback(null, true);
    }
    
    if (allowedOrigins.some(allowed => origin.startsWith(allowed))) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  maxAge: 86400 // 24 hours
};

app.use(cors(corsOptions));

// Basic middleware
app.use(cookieParser());
app.use(express.json({ limit: "24kb" }));
app.use(express.urlencoded({ extended: true, limit: "16kb" }));

// CORS debugging middleware
app.use((req, res, next) => {
  if (IS_PRODUCTION) {
    console.log(`🔍 ${req.method} ${req.path} from ${req.get('origin') || 'no-origin'}`);
  }
  next();
});

// API routes
app.use('/api/auth', authRouters);
app.use('/api/menu', menuItemRouters);
app.use('/api/orders', orderRouters);

// Invalid route handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found",
    path: req.path
  });
});

// Global error handler
app.use(GlobelError);

export default app;
