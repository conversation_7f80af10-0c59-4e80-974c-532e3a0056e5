import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export async function validateStaticFiles(requiredAssets) {
  try {
    const assetsDir = path.join(__dirname, '../../../../client/src/assets');
    const missingAssets = requiredAssets.filter(
      asset => !fs.existsSync(path.join(assetsDir, asset))
    );
    
    if (missingAssets.length === 0) {
      return { success: true };
    }
    return { 
      success: false, 
      error: `Missing static files: ${missingAssets.join(', ')}` 
    };
  } catch (error) {
    return { 
      success: false, 
      error: `Static files check failed: ${error.message}` 
    };
  }
}
