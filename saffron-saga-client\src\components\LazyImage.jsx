import React from 'react';
import { assets } from '../assets/assets';
import { useImageLazyLoading } from '../hooks/useLazyLoading';
import './LazyImage.css';

const LazyImage = ({
  src,
  alt,
  className = '',
  id = '',
  fallbackSrc = assets.defaultImage,
  placeholder = null,
  onLoad = () => {},
  onError = () => {},
  rootMargin = '100px',
  threshold = 0.1,
  ...props
}) => {
  const {
    elementRef,
    isLoaded,
    hasError,
    imageSrc,
    handleLoad: hookHandleLoad,
    handleError: hookHandleError,
    shouldLoad
  } = useImageLazyLoading(src, fallbackSrc, { rootMargin, threshold });

  const handleLoad = () => {
    hookHandleLoad();
    onLoad();
  };

  const handleError = () => {
    hookHandleError();
    onError();
  };

  return (
    <div
      ref={elementRef}
      className={`lazy-image-container ${className}`}
      id={id}
      style={props.style}
    >
      {/* Placeholder while loading */}
      {!isLoaded && (
        <div className="lazy-image-placeholder">
          {placeholder || (
            <div className="lazy-image-loading-text">
              <div className="lazy-image-spinner" />
              <div>Loading...</div>
            </div>
          )}
        </div>
      )}

      {/* Actual image - only load when in view */}
      {shouldLoad && (
        <img
          src={imageSrc}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          className={`lazy-image ${isLoaded ? 'loaded' : 'loading'}`}
          {...props}
        />
      )}
    </div>
  );
};

export default LazyImage;
