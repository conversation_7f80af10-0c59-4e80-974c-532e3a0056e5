import React, { useState, useEffect } from "react";
import "./BannerCarousel.css";

const BannerCarousel = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      id: 1,
      title: "🎉 Celebrate Your Birthday at Saffron Saga",
      subtitle:
        "Make your special day unforgettable with our premium dining experience",
      ctaText: "BOOK NOW",
      ctaAction: () => {
        alert(
          "🎉 Birthday booking feature coming soon! Call us at +91-XXXXXXXXXX to book your celebration."
        );
      },
      backgroundClass: "slide-birthday",
      icon: "🎉",
      imageUrl:
        "https://firebasestorage.googleapis.com/v0/b/saffron-saga-3a77e.firebasestorage.app/o/banner-wbsite.png?alt=media&token=149c2e67-d17d-492a-8746-be86462f6e61",
    },
    {
      id: 2,
      title: "🎂 Free Cake + Custom Decor",
      subtitle:
        "Complimentary birthday cake and personalized decorations for your celebration",
      ctaText: "GRAB THE OFFER",
      ctaAction: () => {
        alert(
          "🎂 Special Birthday Offer! Free cake and custom decorations with advance booking. Terms & conditions apply."
        );
      },
      backgroundClass: "slide-offer",
      icon: "🎂",
      imageUrl:
        "https://firebasestorage.googleapis.com/v0/b/saffron-saga-3a77e.firebasestorage.app/o/banner-wbsite.png?alt=media&token=149c2e67-d17d-492a-8746-be86462f6e61",
    },
    {
      id: 3,
      title: "📅 Reserve Your Special Day",
      subtitle:
        "Book your birthday celebration in advance and let us handle everything",
      ctaText: "CHECK AVAILABILITY",
      ctaAction: () => {
        alert(
          "📅 Check availability for your special day! Contact us to reserve your preferred date and time."
        );
      },
      backgroundClass: "slide-booking",
      icon: "📅",
      imageUrl:
        "https://firebasestorage.googleapis.com/v0/b/saffron-saga-3a77e.firebasestorage.app/o/banner-wbsite.png?alt=media&token=149c2e67-d17d-492a-8746-be86462f6e61",
    },
  ];

  // Auto-slide functionality
  useEffect(() => {
    const slideInterval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 10000); // Change slide every 5 seconds

    return () => clearInterval(slideInterval);
  }, [slides.length]);

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  return (
    <div
      className="banner-carousel"
      style={{
        width: "100%",
        height: "300px",
        position: "relative",
        overflow: "hidden",
        marginBottom: "1rem",
        boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
        borderRadius: "1rem",
        flexShrink: 0,
        display: "block",
        visibility: "visible",
        zIndex: 1,
      }}
    >
      <div
        className="carousel-container"
        style={{
          width: "100%",
          height: "100%",
          position: "relative",
        }}
      >
        {/* Slides */}
        <div
          className="slides-wrapper"
          style={{
            transform: `translateX(-${currentSlide * 100}%)`,
            display: "flex",
            width: "300%",
            height: "100%",
            transition: "transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
          }}
        >
          {slides.map((slide) => (
            <div
              key={slide.id}
              className={`slide ${slide.backgroundClass}`}
              style={{
                width: "33.333%",
                height: "100%",
                position: "relative",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: "no-repeat",
              }}
            >
              <div className="slide-overlay"></div>
              <div
                className="slide-content"
                style={{
                  position: "relative",
                  width: "100%",
                  height: "100%",
                  padding: 0,
                }}
              >
                {/* Full-size Banner Image */}
                <div
                  className="banner-image-container"
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    overflow: "hidden",
                  }}
                >
                  <img
                    src={slide.imageUrl}
                    alt="Saffron Saga Birthday Banner"
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                      objectPosition: "center",
                    }}
                    onError={(e) => {
                      e.target.style.display = "none";
                      e.target.nextSibling.style.display = "flex";
                    }}
                  />
                  {/* Fallback background if image fails to load */}
                  <div
                    style={{
                      display: "none",
                      width: "100%",
                      height: "100%",
                      background:
                        "linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B8860B 100%)",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "#FFD700",
                      fontSize: "2rem",
                      fontWeight: "bold",
                      textAlign: "center",
                      textShadow: "2px 2px 4px rgba(0, 0, 0, 0.5)",
                    }}
                  >
                    🎉 {slide.title} 🎂
                  </div>
                </div>

                {/* CTA Button positioned on top of image */}
                <button
                  className="cta-button"
                  onClick={slide.ctaAction}
                  style={{
                    position: "absolute",
                    bottom: "2rem",
                    left: "50%",
                    transform: "translateX(-50%)",
                    background: "linear-gradient(45deg, #FFD700, #FFA500)",
                    color: "#8B0000",
                    border: "none",
                    padding: "1rem 2.5rem",
                    fontSize: "1.1rem",
                    fontWeight: "700",
                    borderRadius: "50px",
                    cursor: "pointer",
                    transition: "all 0.3s ease",
                    textTransform: "uppercase",
                    letterSpacing: "1px",
                    boxShadow: "0 4px 15px rgba(0, 0, 0, 0.3)",
                    zIndex: 10,
                    backdropFilter: "blur(5px)",
                    border: "2px solid rgba(255, 255, 255, 0.2)",
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform =
                      "translateX(-50%) translateY(-2px)";
                    e.target.style.boxShadow = "0 6px 20px rgba(0, 0, 0, 0.4)";
                    e.target.style.background =
                      "linear-gradient(45deg, #FFA500, #FFD700)";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = "translateX(-50%) translateY(0)";
                    e.target.style.boxShadow = "0 4px 15px rgba(0, 0, 0, 0.3)";
                    e.target.style.background =
                      "linear-gradient(45deg, #FFD700, #FFA500)";
                  }}
                >
                  {slide.ctaText}
                </button>

                {/* Decorative elements */}
                <div
                  className="slide-visual"
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    pointerEvents: "none",
                    zIndex: 1,
                  }}
                >
                  <div className="visual-decoration">
                    <div className="balloon balloon-1"></div>
                    <div className="balloon balloon-2"></div>
                    <div className="balloon balloon-3"></div>
                    <div className="confetti confetti-1"></div>
                    <div className="confetti confetti-2"></div>
                    <div className="confetti confetti-3"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Arrows */}
        <button className="nav-arrow nav-arrow-left" onClick={prevSlide}>
          <i className="ri-arrow-left-s-line"></i>
        </button>
        <button className="nav-arrow nav-arrow-right" onClick={nextSlide}>
          <i className="ri-arrow-right-s-line"></i>
        </button>

        {/* Dots Indicator */}
        <div className="dots-container">
          {slides.map((_, index) => (
            <button
              key={index}
              className={`dot ${index === currentSlide ? "active" : ""}`}
              onClick={() => goToSlide(index)}
            ></button>
          ))}
        </div>

        {/* Logo */}
        <div className="carousel-logo">
          <span className="logo-text">Saffron Saga</span>
        </div>
      </div>
    </div>
  );
};

export default BannerCarousel;
