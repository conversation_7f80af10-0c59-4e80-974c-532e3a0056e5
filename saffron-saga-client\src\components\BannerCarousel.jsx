import React, { useState, useEffect } from 'react';
import './BannerCarousel.css';

const BannerCarousel = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  console.log('🎉 BannerCarousel rendering at:', new Date().toLocaleTimeString());

  const slides = [
    {
      id: 1,
      title: "🎉 Celebrate Your Birthday at Saffron Saga",
      subtitle: "Make your special day unforgettable with our premium dining experience",
      ctaText: "BOOK NOW",
      ctaAction: () => {
        alert("🎉 Birthday booking feature coming soon! Call us at +91-XXXXXXXXXX to book your celebration.");
      },
      backgroundClass: "slide-birthday",
      icon: "🎉"
    },
    {
      id: 2,
      title: "🎂 Free Cake + Custom Decor",
      subtitle: "Complimentary birthday cake and personalized decorations for your celebration",
      ctaText: "GRAB THE OFFER",
      ctaAction: () => {
        alert("🎂 Special Birthday Offer! Free cake and custom decorations with advance booking. Terms & conditions apply.");
      },
      backgroundClass: "slide-offer",
      icon: "🎂"
    },
    {
      id: 3,
      title: "📅 Reserve Your Special Day",
      subtitle: "Book your birthday celebration in advance and let us handle everything",
      ctaText: "CHECK AVAILABILITY",
      ctaAction: () => {
        alert("📅 Check availability for your special day! Contact us to reserve your preferred date and time.");
      },
      backgroundClass: "slide-booking",
      icon: "📅"
    }
  ];

  // Auto-slide functionality
  useEffect(() => {
    const slideInterval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(slideInterval);
  }, [slides.length]);

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  return (
    <div className="banner-carousel" style={{
      width: '100%',
      height: '400px',
      position: 'relative',
      overflow: 'hidden',
      marginBottom: '1rem',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
      borderRadius: '1rem',
      flexShrink: 0,
      display: 'block',
      visibility: 'visible',
      zIndex: 1
    }}>
      {/* Always visible fallback */}
      <div style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        background: '#FFD700',
        color: '#8B0000',
        padding: '5px 10px',
        borderRadius: '5px',
        fontSize: '12px',
        fontWeight: 'bold',
        zIndex: 10
      }}>
        CAROUSEL ACTIVE
      </div>

      <div className="carousel-container" style={{
        width: '100%',
        height: '100%',
        position: 'relative'
      }}>
        {/* Slides */}
        <div className="slides-wrapper" style={{
          transform: `translateX(-${currentSlide * 100}%)`,
          display: 'flex',
          width: '300%',
          height: '100%',
          transition: 'transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)'
        }}>
          {slides.map((slide) => (
            <div key={slide.id} className={`slide ${slide.backgroundClass}`} style={{
              width: '33.333%',
              height: '100%',
              position: 'relative',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }}>
              <div className="slide-overlay"></div>
              <div className="slide-content">
                <div className="slide-text">
                  <div className="slide-icon">{slide.icon}</div>
                  <h1 className="slide-title">{slide.title}</h1>
                  <p className="slide-subtitle">{slide.subtitle}</p>
                  <button
                    className="cta-button"
                    onClick={slide.ctaAction}
                  >
                    {slide.ctaText}
                  </button>
                </div>
                <div className="slide-visual">
                  <div className="visual-decoration">
                    <div className="balloon balloon-1"></div>
                    <div className="balloon balloon-2"></div>
                    <div className="balloon balloon-3"></div>
                    <div className="confetti confetti-1"></div>
                    <div className="confetti confetti-2"></div>
                    <div className="confetti confetti-3"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Arrows */}
        <button className="nav-arrow nav-arrow-left" onClick={prevSlide}>
          <i className="ri-arrow-left-s-line"></i>
        </button>
        <button className="nav-arrow nav-arrow-right" onClick={nextSlide}>
          <i className="ri-arrow-right-s-line"></i>
        </button>

        {/* Dots Indicator */}
        <div className="dots-container">
          {slides.map((_, index) => (
            <button
              key={index}
              className={`dot ${index === currentSlide ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
            ></button>
          ))}
        </div>

        {/* Logo */}
        <div className="carousel-logo">
          <span className="logo-text">Saffron Saga</span>
        </div>
      </div>
    </div>
  );
};

export default BannerCarousel;