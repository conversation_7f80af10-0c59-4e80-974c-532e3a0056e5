import React, { useState, useEffect } from 'react';
import './BannerCarousel.css';

const BannerCarousel = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Debug log to check if component is rendering
  console.log('BannerCarousel rendering, currentSlide:', currentSlide);

  const slides = [
    {
      id: 1,
      title: "🎉 Celebrate Your Birthday at Saffron Saga",
      subtitle: "Make your special day unforgettable with our premium dining experience",
      ctaText: "BOOK NOW",
      ctaAction: () => {
        // You can add booking functionality here
        alert("🎉 Birthday booking feature coming soon! Call us at +91-XXXXXXXXXX to book your celebration.");
      },
      backgroundClass: "slide-birthday",
      icon: "🎉"
    },
    {
      id: 2,
      title: "🎂 Free Cake + Custom Decor",
      subtitle: "Complimentary birthday cake and personalized decorations for your celebration",
      ctaText: "GRAB THE OFFER",
      ctaAction: () => {
        alert("🎂 Special Birthday Offer! Free cake and custom decorations with advance booking. Terms & conditions apply.");
      },
      backgroundClass: "slide-offer",
      icon: "🎂"
    },
    {
      id: 3,
      title: "📅 Reserve Your Special Day",
      subtitle: "Book your birthday celebration in advance and let us handle everything",
      ctaText: "CHECK AVAILABILITY",
      ctaAction: () => {
        alert("📅 Check availability for your special day! Contact us to reserve your preferred date and time.");
      },
      backgroundClass: "slide-booking",
      icon: "📅"
    }
  ];

  // Auto-slide functionality
  useEffect(() => {
    const slideInterval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(slideInterval);
  }, [slides.length]);

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };



  return (
    <div className="banner-carousel">
      {/* Fallback content to ensure visibility */}
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        color: '#FFD700',
        fontSize: '2rem',
        fontWeight: 'bold',
        zIndex: 10,
        textAlign: 'center'
      }}>
        🎉 Saffron Saga Birthday Celebrations! 🎂
      </div>
      <div className="carousel-container">
        {/* Slides */}
        <div className="slides-wrapper" style={{ transform: `translateX(-${currentSlide * 100}%)` }}>
          {slides.map((slide) => (
            <div key={slide.id} className={`slide ${slide.backgroundClass}`}>
              <div className="slide-overlay"></div>
              <div className="slide-content">
                <div className="slide-text">
                  <div className="slide-icon">{slide.icon}</div>
                  <h1 className="slide-title">{slide.title}</h1>
                  <p className="slide-subtitle">{slide.subtitle}</p>
                  <button
                    className="cta-button"
                    onClick={slide.ctaAction}
                  >
                    {slide.ctaText}
                  </button>
                </div>
                <div className="slide-visual">
                  <div className="visual-decoration">
                    <div className="balloon balloon-1"></div>
                    <div className="balloon balloon-2"></div>
                    <div className="balloon balloon-3"></div>
                    <div className="confetti confetti-1"></div>
                    <div className="confetti confetti-2"></div>
                    <div className="confetti confetti-3"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Arrows */}
        <button className="nav-arrow nav-arrow-left" onClick={prevSlide}>
          <i className="ri-arrow-left-s-line"></i>
        </button>
        <button className="nav-arrow nav-arrow-right" onClick={nextSlide}>
          <i className="ri-arrow-right-s-line"></i>
        </button>

        {/* Dots Indicator */}
        <div className="dots-container">
          {slides.map((_, index) => (
            <button
              key={index}
              className={`dot ${index === currentSlide ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
            ></button>
          ))}
        </div>

        {/* Logo */}
        <div className="carousel-logo">
          <span className="logo-text">Saffron Saga</span>
        </div>
      </div>
    </div>
  );
};

export default BannerCarousel;