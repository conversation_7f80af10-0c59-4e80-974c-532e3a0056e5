services:
  # Backend API Service
  - type: web
    name: saffron-saga-server
    env: node
    plan: starter
    region: ohio
    buildCommand: npm ci --only=production
    startCommand: npm start
    healthCheckPath: /health
    rootDir: server
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 8080
      - key: CORS_ORIGIN
        value: https://saffron-saga-client.onrender.com
      - key: RATE_LIMIT_WINDOW_MS
        value: 900000
      - key: RATE_LIMIT_MAX_REQUESTS
        value: 100
      - key: SALT_ROUNDS
        value: 12
      - key: TRUST_PROXY
        value: true
      - key: JWT_SECRET
        generateValue: true
      - key: SESSION_SECRET
        generateValue: true
      - fromGroup: saffron-saga-secrets

  # Frontend Static Site
  - type: web
    name: saffron-saga-client
    env: static
    plan: starter
    region: ohio
    buildCommand: npm ci && npm run build
    staticPublishPath: ./dist
    rootDir: client
    envVars:
      - key: NODE_ENV
        value: production
      - key: VITE_API_BASE_URL
        value: https://saffron-saga-server.onrender.com
    routes:
      - type: rewrite
        source: /*
        destination: /index.html
    headers:
      - path: /*
        name: Cache-Control
        value: no-cache
      - path: /assets/*
        name: Cache-Control
        value: public, max-age=31536000

# Environment Variable Groups
envVarGroups:
  - name: saffron-saga-secrets
    envVars:
      # Database Configuration
      - key: MONGODB_URI
        sync: false
      - key: DB_NAME
        sync: false
      # Email Service Configuration (Brevo)
      - key: BREVO_EMAIL
        sync: false
      - key: BREVO_USER
        sync: false
      - key: BREVO_PASS
        sync: false
      - key: RESTAURANT_EMAIL
        sync: false
