services:
  - type: web
    name: saffron-saga-server
    env: node
    plan: starter
    region: ohio
    buildCommand: cd server && npm install && npm run build
    startCommand: cd server && npm run start:prod
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 8080
      - key: MONGODB_URI
        sync: false
      - key: DB_NAME
        sync: false
      - key: JWT_SECRET
        generateValue: true
      - key: SESSION_SECRET
        generateValue: true
      - key: CORS_ORIGIN
        value: https://saffron-saga-client.onrender.com
      - key: RATE_LIMIT_WINDOW_MS
        value: 900000
      - key: RATE_LIMIT_MAX_REQUESTS
        value: 100
      - key: SALT_ROUNDS
        value: 10
      - fromGroup: saffron-saga-secrets
    autoDeploy: true
    numInstances: 1
    healthCheckTimeout: 300
    disk:
      name: uploads
      mountPath: /opt/render/project/src/server/uploads
      sizeGB: 1

  - type: web
    name: saffron-saga-client
    env: node
    plan: starter
    region: ohio
    buildCommand: cd client && npm install && npm run build
    staticPublishPath: ./client/dist
    envVars:
      - key: NODE_ENV
        value: production
      - key: VITE_API_BASE_URL
        value: https://saffron-saga-server.onrender.com
    routes:
      - type: rewrite
        source: /*
        destination: /index.html
    headers:
      - path: /*
        name: Cache-Control
        value: no-cache
      - path: /assets/*
        name: Cache-Control
        value: public, max-age=31536000, immutable

envVarGroups:
  - name: saffron-saga-secrets
    envVars:
      - key: BREVO_EMAIL
        sync: false
      - key: BREVO_USER
        sync: false
      - key: BREVO_PASS
        sync: false
      - key: RESTAURANT_EMAIL
        sync: false
