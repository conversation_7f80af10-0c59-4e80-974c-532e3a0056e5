#!/usr/bin/env node

const axios = require('axios');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables from server's .env.production
const envPath = path.join(process.cwd(), 'server', '.env.production');
if (fs.existsSync(envPath)) {
  const envConfig = dotenv.parse(fs.readFileSync(envPath));
  // Update process.env with values from .env.production
  for (const key in envConfig) {
    process.env[key] = envConfig[key];
  }
} else {
  console.warn('⚠️ .env.production file not found in server directory');
}

// Use API_URL from environment or default to localhost
const BASE_URL = process.env.API_URL || 'http://localhost:8080';
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:5173';

// Required environment variables
const requiredEnvVars = {
  server: [
    { name: 'MONGODB_URI', validate: async (val) => val.startsWith('mongodb') },
    { name: 'JWT_SECRET', validate: (val) => val.length >= 32 },
    { name: 'CORS_ORIGIN', validate: (val) => val.includes('http') },
    { name: 'BREVO_EMAIL', validate: (val) => val.includes('@') },
    { name: 'BREVO_USER', validate: (val) => val.length > 0 },
    { name: 'BREVO_PASS', validate: (val) => val.length > 0 }
  ]
};

// Update validation configuration with longer timeouts for production
const CONFIG = {
  timeouts: {
    api: 10000, // 10 seconds
    db: 15000   // 15 seconds
  },
  retries: {
    api: 5,     // More retries for production
    db: 3
  },
  healthCheck: {
    minHeapAvailable: 100 * 1024 * 1024, // 100MB
    maxResponseTime: 1000 // 1 second for production
  }
};

async function retry(fn, maxRetries, delay = 2000) {
  let lastError;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
        console.log(`Retry ${i + 1}/${maxRetries}...`);
      }
    }
  }
  
  throw lastError;
}

async function validateDeployment() {
  try {
    console.log('🚀 Starting deployment validation...');

    // Step 1: Check if environment variables are set
    console.log('\n🔐 Validating environment variables...');
    
    // Load and validate server environment variables
    const serverEnv = dotenv.parse(fs.readFileSync(envPath));
    const missingServerVars = [];
    const invalidServerVars = [];

    for (const envVar of requiredEnvVars.server) {
      const value = serverEnv[envVar.name];
      if (!value) {
        missingServerVars.push(envVar.name);
      } else {
        try {
          const isValid = await envVar.validate(value);
          if (!isValid) {
            invalidServerVars.push(envVar.name);
          }
        } catch (error) {
          invalidServerVars.push(`${envVar.name} (${error.message})`);
        }
      }
    }

    if (missingServerVars.length > 0) {
      throw new Error(`Missing required server environment variables: ${missingServerVars.join(', ')}`);
    }

    if (invalidServerVars.length > 0) {
      throw new Error(`Invalid server environment variables: ${invalidServerVars.join(', ')}`);
    }

    console.log('✅ Server environment variables validated');

    // Check if the server is running
    console.log('\n🔄 Checking server status...');
    let serverRunning = false;
    let retries = CONFIG.retries.api;
    
    while (retries > 0 && !serverRunning) {
      try {
        const response = await axios.get(`${BASE_URL}/health`, {
          timeout: CONFIG.timeouts.api,
          validateStatus: (status) => status === 200
        });
        
        if (response.data.success && response.data.status === 'healthy') {
          serverRunning = true;
          console.log('✅ Server is running');
          break;
        } else {
          throw new Error('Server health check failed');
        }
      } catch (error) {
        retries--;
        if (retries === 0) {
          throw new Error(`API server is not responding correctly: ${error.message}`);
        }
        console.log(`Retrying server check... (${retries} attempts remaining)`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // Check health endpoint with validation
    const healthCheck = await retry(async () => {
      const response = await axios.get(`${BASE_URL}/health?validate=true`, {
        timeout: CONFIG.timeouts.api,
        validateStatus: (status) => status === 200
      });
      if (!response.data.success) {
        throw new Error('Health check validation failed');
      }
      return response.data;
    }, CONFIG.retries.api);

    // Display health information
    console.log('\n🏥 Server Health Check:');
    console.log(`✅ Uptime: ${Math.floor(healthCheck.uptime / 3600)}h ${Math.floor((healthCheck.uptime % 3600) / 60)}m`);
    console.log(`✅ Memory: ${Math.round(healthCheck.memory.heapUsed / 1024 / 1024)}MB/${Math.round(healthCheck.memory.heapTotal / 1024 / 1024)}MB`);

    if (healthCheck.memory.heapUsed > CONFIG.healthCheck.minHeapAvailable) {
      console.warn('⚠️ High memory usage detected');
    }

    // Validate critical endpoints
    console.log('\n🎯 Validating critical endpoints...');
    const endpoints = [
      { url: '/api/menu', name: 'Menu items' },
      { url: '/api/auth/status', name: 'Auth status' },
      { url: '/api/orders/status', name: 'Orders status' }
    ];

    for (const endpoint of endpoints) {
      await retry(async () => {
        const startTime = Date.now();
        const response = await axios.get(`${BASE_URL}${endpoint.url}`, {
          timeout: CONFIG.timeouts.api,
          validateStatus: status => status < 500
        });
        const responseTime = Date.now() - startTime;
        
        console.log(`✅ ${endpoint.name} endpoint responding (${response.status}) in ${responseTime}ms`);
        
        if (responseTime > CONFIG.healthCheck.maxResponseTime) {
          console.warn(`⚠️ Slow response time for ${endpoint.name}: ${responseTime}ms`);
        }
      }, CONFIG.retries.api);
    }

    // Check build artifacts
    console.log('\n📦 Checking build artifacts...');
    const clientBuildPath = path.join(process.cwd(), 'client', 'dist');
    
    if (!fs.existsSync(clientBuildPath)) {
      console.log('⚠️ Client build not found, running build...');
      try {
        process.chdir(path.join(process.cwd(), 'client'));
        execSync('npm run build', { stdio: 'inherit' });
        process.chdir(path.join(process.cwd(), '..'));
        console.log('✅ Client build completed');
      } catch (error) {
        throw new Error(`Client build failed: ${error.message}`);
      }
    }

    // Validate the build
    if (fs.existsSync(clientBuildPath)) {
      console.log('✅ Client build exists');
      
      if (fs.existsSync(path.join(clientBuildPath, 'index.html'))) {
        console.log('✅ index.html found');
      } else {
        throw new Error('index.html missing from client build');
      }
      
      const assets = fs.readdirSync(path.join(clientBuildPath, 'assets'));
      if (assets.length > 0) {
        console.log(`✅ ${assets.length} asset files found`);
      } else {
        console.warn('⚠️ No assets found in build');
      }
    }

    console.log('\n✅ Deployment validation completed successfully!');
  } catch (error) {
    console.error('\n❌ Deployment validation failed:', error.message);
    if (error.message.includes('API server is not running')) {
      console.log('\nMake sure the server is running with:');
      console.log('cd server');
      console.log('npm run start:prod\n');
      console.log('Then verify the health endpoint is accessible at:');
      console.log(`${BASE_URL}/health`);
    }
    process.exit(1);
  }
}

validateDeployment();
