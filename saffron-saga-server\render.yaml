services:
  - type: web
    name: saffron-saga-server
    env: node
    plan: starter
    region: ohio
    buildCommand: npm ci --only=production
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 8080
      - key: CORS_ORIGIN
        value: https://saffron-saga-client.vercel.app
      - key: RATE_LIMIT_WINDOW_MS
        value: 900000
      - key: RATE_LIMIT_MAX_REQUESTS
        value: 100
      - key: SALT_ROUNDS
        value: 12
      - key: TRUST_PROXY
        value: true
      - key: JWT_SECRET
        generateValue: true
      - key: SESSION_SECRET
        generateValue: true
      - fromGroup: saffron-saga-secrets

# Environment Variable Groups
envVarGroups:
  - name: saffron-saga-secrets
    envVars:
      # Database Configuration
      - key: MONGODB_URI
        sync: false
      - key: DB_NAME
        sync: false
      # Email Service Configuration (Brevo)
      - key: BREVO_EMAIL
        sync: false
      - key: BREVO_USER
        sync: false
      - key: BREVO_PASS
        sync: false
      - key: RES<PERSON><PERSON>ANT_EMAIL
        sync: false
