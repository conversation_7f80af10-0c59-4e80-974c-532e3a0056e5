import React, { createContext, useContext, useState, useEffect } from 'react';
import { getConnectionBasedOptimization, imagePerformanceTracker } from '../utils/imageOptimization';

const ImageLoadingContext = createContext();

export const useImageLoading = () => {
  const context = useContext(ImageLoadingContext);
  if (!context) {
    throw new Error('useImageLoading must be used within an ImageLoadingProvider');
  }
  return context;
};

export const ImageLoadingProvider = ({ children }) => {
  const [settings, setSettings] = useState({
    lazyLoading: true,
    performanceTracking: false,
    adaptiveQuality: true,
    preloadCritical: true,
    rootMargin: '100px',
    threshold: 0.1
  });

  const [connectionInfo, setConnectionInfo] = useState(null);
  const [performanceMetrics, setPerformanceMetrics] = useState(null);

  // Monitor connection changes
  useEffect(() => {
    const updateConnectionInfo = () => {
      const optimization = getConnectionBasedOptimization();
      setConnectionInfo(optimization);
    };

    updateConnectionInfo();

    // Listen for connection changes
    if (navigator.connection) {
      navigator.connection.addEventListener('change', updateConnectionInfo);
      return () => {
        navigator.connection.removeEventListener('change', updateConnectionInfo);
      };
    }
  }, []);

  // Update performance metrics periodically
  useEffect(() => {
    if (!settings.performanceTracking) return;

    const interval = setInterval(() => {
      const metrics = imagePerformanceTracker.getMetrics();
      setPerformanceMetrics(metrics);
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [settings.performanceTracking]);

  const updateSettings = (newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const resetPerformanceMetrics = () => {
    imagePerformanceTracker.reset();
    setPerformanceMetrics(null);
  };

  const getOptimalSettings = () => {
    if (!connectionInfo) return settings;

    // Adjust settings based on connection
    const optimal = { ...settings };
    
    if (connectionInfo.quality < 65) {
      // Slow connection - more aggressive lazy loading
      optimal.rootMargin = '50px';
      optimal.threshold = 0.05;
      optimal.preloadCritical = false;
    } else if (connectionInfo.quality >= 85) {
      // Fast connection - less aggressive lazy loading
      optimal.rootMargin = '200px';
      optimal.threshold = 0.2;
      optimal.preloadCritical = true;
    }

    return optimal;
  };

  const value = {
    settings,
    updateSettings,
    connectionInfo,
    performanceMetrics,
    resetPerformanceMetrics,
    getOptimalSettings
  };

  return (
    <ImageLoadingContext.Provider value={value}>
      {children}
    </ImageLoadingContext.Provider>
  );
};

export default ImageLoadingContext;
