{"name": "saffron-saga-server", "version": "1.0.0", "description": "Saffron Saga Restaurant Backend API", "type": "module", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "NODE_ENV=production node src/server.js", "start:prod": "NODE_ENV=production node src/server.js", "build": "echo 'No build step required for Node.js'", "test": "echo 'No tests specified'", "db:setup": "node src/scripts/dbSetup.js", "db:seed": "node src/scripts/seedData.js", "db:migrate": "node src/scripts/migrate.js"}, "engines": {"node": ">=20.11.0", "npm": ">=9.0.0"}, "keywords": ["restaurant", "api", "backend", "nodejs", "mongodb", "express"], "author": "Saffron Saga Team", "license": "ISC", "dependencies": {"bcrypt": "^6.0.0", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.1", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0", "nodemailer": "^7.0.3"}, "devDependencies": {"cross-env": "^7.0.3", "nodemon": "^3.1.7", "prettier": "^3.5.3"}}