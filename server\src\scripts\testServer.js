#!/usr/bin/env node

/**
 * Minimal test server
 */

import express from 'express';
import cors from 'cors';
import { MenuItem } from '../models/menuItem.model.js';
import connectToDB from '../db/db.js';

const app = express();
const PORT = 4003;

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Test server is running' });
});

// Menu route
app.get('/api/menu', async (req, res) => {
  try {
    console.log('📥 Received request for /api/menu');
    const menuItems = await MenuItem.find({ isAvailable: true });
    console.log(`📊 Found ${menuItems.length} menu items`);
    
    res.json({
      success: true,
      message: "Fetched all menu items successfully",
      data: menuItems,
    });
  } catch (error) {
    console.error('❌ Error in /api/menu:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// Error handler
app.use((err, req, res, next) => {
  console.error('❌ Server error:', err);
  res.status(500).json({ error: err.message });
});

async function startTestServer() {
  try {
    console.log('🔧 Starting test server...');
    
    // Connect to database
    await connectToDB();
    console.log('✅ Database connected');
    
    // Start server
    app.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 Test server running on http://localhost:${PORT}`);
      console.log(`📊 Health: http://localhost:${PORT}/health`);
      console.log(`🍽️  Menu: http://localhost:${PORT}/api/menu`);
    });
    
  } catch (error) {
    console.error('❌ Failed to start test server:', error);
    process.exit(1);
  }
}

startTestServer();
