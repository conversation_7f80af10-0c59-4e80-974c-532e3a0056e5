import { body, param } from "express-validator";

export const menuItemValidation = [
  body("name")
    .notEmpty()
    .withMessage("Name is required")
    .isLength({ min: 2, max: 150 })
    .withMessage("Name must be 2 to 150 characters"),

  body("description")
    .notEmpty()
    .withMessage("Description is required")
    .isLength({ min: 20, max: 800 })
    .withMessage("Description must be 20 to 800 characters"),

  body("category")
    .notEmpty()
    .withMessage("Category is required")
    .isLength({ min: 2, max: 200 })
    .withMessage("Description must be 10 to 200 characters"),

  body("price")
    .notEmpty()
    .withMessage("Price is required")
    .isFloat({ gt: 0 }) // must be a float > 0
    .withMessage("Price must be a positive number"),

  body("quantity")
    .notEmpty()
    .withMessage("Quantity is required")
    .isFloat({ gt: 0, lt: 100 }) // must be a float > 0 < 100
    .withMessage("Quantity must be a positive number less than 100"),

  body("unit")
    .notEmpty()
    .withMessage("Unit is required")
    .isLength({ min: 2, max: 30 })
    .withMessage("Unit must be 2 to 30 characters"),
];

export const validateObjectIdParam = [
  param("id").isMongoId().withMessage("Invalid ObjectId format"),
];
