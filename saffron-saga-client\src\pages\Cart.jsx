import React, { useContext } from "react";
import Navbar from "../components/Navbar";
import { useNavigate } from "react-router-dom";
import "./cart.css";
import { CartContext } from "../context/CartContext";
import { MenuContext } from "../context/MenuContext";

const Cart = () => {
  const navigate = useNavigate();
  const { cart, setCart } = useContext(CartContext);
  const { menuItems } = useContext(MenuContext);

  const increment = (id) => {
    const updatedCart = cart.map((item) =>
      item._id === id ? { ...item, quantity: item.quantity + 1 } : item
    );
    setCart(updatedCart);
  };

  const decrement = (id) => {
    const updatedCart = cart
      .map((item) =>
        item._id === id ? { ...item, quantity: item.quantity - 1 } : item
      )
      .filter((item) => item.quantity > 0);
    setCart(updatedCart);
  };

  const handleBackClick = () => {
    navigate("/");
  };

  const handleNextClick = () => {
    navigate("/order"); // Change to your desired next step route
  };

  const getItemDetails = (id) => menuItems.find((item) => item._id === id);

  const totalPrice = cart.reduce((acc, cartItem) => {
    const item = getItemDetails(cartItem._id);
    return item ? acc + item.price * cartItem.quantity : acc;
  }, 0);

  return (
    <div className="page-container">
      <Navbar />
      <div className="cart-conatiner">
        <button onClick={handleBackClick} className="back-btn">
          <i className="ri-arrow-left-line"></i>Back
        </button>
        <div className="order-details">
          <h2 className="heading">Order details</h2>
          {cart.length === 0 ? (
            <p>Your cart is empty</p>
          ) : (
            cart.map((cartItem) => {
              const item = getItemDetails(cartItem._id);
              if (!item) return null;

              return (
                <div className="detail-card" key={cartItem._id}>
                  <div className="card-top">
                    <h4>{item.name}</h4>
                    <h3>₹{(item.price * cartItem.quantity).toFixed(2)}</h3>
                  </div>
                  <div className="card-bottom">
                    <div className="counter">
                      <button onClick={() => decrement(cartItem._id)}>
                        <i className="ri-subtract-line"></i>
                      </button>
                      <span>{cartItem.quantity}</span>
                      <button onClick={() => increment(cartItem._id)}>
                        <i className="ri-add-line"></i>
                      </button>
                    </div>
                  </div>
                </div>
              );
            })
          )}

          {cart.length > 0 && (
            <div className="bottom-bar">
              <div className="bottom-bar-left">
                <span>Total</span>
                <span className="price">₹{totalPrice.toFixed(2)}</span>
              </div>
              <button className="next-btn" onClick={handleNextClick}>
               Place Order 
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Cart;
