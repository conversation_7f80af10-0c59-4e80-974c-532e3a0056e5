module.exports = {
  apps: [
    {
      name: 'saffron-saga-server',
      script: 'src/server.js',
      instances: 'max', // Use all available CPU cores
      exec_mode: 'cluster',
      autorestart: true,
      watch: false, // Disable watch in production
      max_memory_restart: '1G',
      env_production: {
        NODE_ENV: 'production',
        PORT: 8080
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 4000
      },
      // Logging
      error_file: 'logs/err.log',
      out_file: 'logs/out.log',
      log_file: 'logs/combined.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      // Advanced Features
      time: true, // Add timestamps in logs
      merge_logs: true,
      
      // Auto restart settings
      watch: false, // Don't watch files in production
      max_restarts: 10,
      restart_delay: 4000,
      
      // Memory management
      max_memory_restart: '500M',
      
      // Advanced settings
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
      
      // Health monitoring
      health_check_grace_period: 3000,
      
      // Environment file
      env_file: '.env.production'
    }
  ],
  
  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server-ip',
      ref: 'origin/main',
      repo: 'https://github.com/your-username/saffron-saga.git',
      path: '/var/www/saffron-saga',
      'post-deploy': 'cd server && npm install --production && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'mkdir -p /var/www/saffron-saga/server/logs'
    }
  }
};
