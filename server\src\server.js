import app from "./app.js";
import http from "http";
import mongoose from "mongoose";
import { NODE_ENV, PORT, IS_PRODUCTION } from "./config/env.js";
import connectToDB from "./db/db.js";

let serverInstance = null;

const startServer = async () => {
  try {
    // Create and start the server
    serverInstance = http.createServer(app);

    serverInstance.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`Port ${PORT} is already in use. Please use a different port.`);
        process.exit(1);
      } else {
        console.error('Server error:', error);
        process.exit(1);
      }
    });

    serverInstance.listen(PORT, '0.0.0.0', () => {
      console.log(`Server running on port ${PORT} in ${NODE_ENV} mode`);
      if (!IS_PRODUCTION) {
        console.log(`Health check: http://localhost:${PORT}/health`);
        console.log(`API Base: http://localhost:${PORT}`);
      }
    });
  } catch (error) {
    console.error('Failed to start server:', error.message);
    process.exit(1);
  }
};

// Graceful shutdown handling
const gracefulShutdown = async (signal) => {
  console.log(`\n${signal} received. Starting graceful shutdown...`);

  try {
    // First check if the server is running
    if (serverInstance && serverInstance.listening) {
      await new Promise((resolve) => {
        serverInstance.close(() => {
          console.log('HTTP server closed.');
          resolve();
        });
      });
    }

    // Then close database connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
      console.log('Database connection closed.');
    }

    console.log('Graceful shutdown completed.');
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', async (err) => {
  console.error('Uncaught Exception:', err);
  await gracefulShutdown('UNCAUGHT_EXCEPTION');
});

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  await gracefulShutdown('UNHANDLED_REJECTION');
});

// Initialize the server
(async () => {
  try {
    if (!IS_PRODUCTION) {
      console.log(`Initializing server - Environment: ${NODE_ENV}, Port: ${PORT}`);
    }

    await connectToDB();
    await startServer();
  } catch (error) {
    console.error('Failed to initialize server:', error);
    process.exit(1);
  }
})();
