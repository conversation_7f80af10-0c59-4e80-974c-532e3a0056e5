// API Configuration for Saffron Saga Client
import axios from 'axios';

// API Base URL - automatically uses environment variable
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://saffron-saga-server.onrender.com';

// Create axios instance with default configuration
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Include cookies for authentication
});

// Request interceptor for debugging in development
apiClient.interceptors.request.use(
  (config) => {
    if (import.meta.env.VITE_NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    }
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    if (import.meta.env.VITE_NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    }
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.data || error.message);
    
    // Handle common error scenarios
    if (error.response?.status === 401) {
      // Unauthorized - redirect to login or clear auth
      console.warn('🔒 Unauthorized access - please login');
    } else if (error.response?.status === 403) {
      // Forbidden
      console.warn('🚫 Access forbidden');
    } else if (error.response?.status >= 500) {
      // Server error
      console.error('🔥 Server error - please try again later');
    }
    
    return Promise.reject(error);
  }
);

// API Endpoints
export const API_ENDPOINTS = {
  // Health check
  HEALTH: '/health',
  HEALTH_DETAILED: '/health?validate=true',

  // Menu endpoints
  MENU_ITEMS: '/api/menu',
  CREATE_MENU_ITEM: '/api/menu/create',
  EDIT_MENU_AVAILABILITY: '/api/menu/edit-avilability',

  // Order endpoints
  CREATE_ORDER: '/api/orders/create',

  // Auth endpoints
  ADMIN_REGISTER: '/api/auth/admin/register',
  ADMIN_LOGIN: '/api/auth/admin/login',
  ADMIN_LOGOUT: '/api/auth/admin/logout',
};

// Export API base URL for use in other components
export { API_BASE_URL };

export default apiClient;
