{"name": "saffron-saga-client", "private": true, "version": "1.0.0", "description": "Saffron Saga Restaurant Frontend Application", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "build:prod": "vite build --mode production", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "preview:prod": "vite preview --mode production", "clean": "<PERSON><PERSON><PERSON> dist", "build:clean": "npm run clean && npm run build:prod"}, "engines": {"node": ">=20.11.0", "npm": ">=9.0.0"}, "dependencies": {"axios": "^1.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1", "react-toastify": "^11.0.5", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "rimraf": "^6.0.1", "vite": "^6.3.5"}, "keywords": ["restaurant", "frontend", "react", "vite", "food-ordering"], "author": "Saffron Saga Team", "license": "ISC"}