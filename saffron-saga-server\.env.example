# Saffron Saga Server Environment Variables

# Server Configuration
NODE_ENV=development
PORT=8080

# Database Configuration
MONGODB_URI=mongodb+srv://ishukumar6949:<EMAIL>
DB_NAME=saffronsaga

# CORS Configuration
CORS_ORIGIN=https://saffron-saga-client-p.vercel.app

# Security Configuration
JWT_SECRET=dev-jwt-secret-not-for-production
SESSION_SECRET=dev-session-secret-not-for-production
SALT_ROUNDS=12
TRUST_PROXY=false

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Email Service Configuration (Brevo)
RESTAURANT_EMAIL=<EMAIL>
BREVO_EMAIL=<EMAIL>
BREVO_USER=<EMAIL>
BREVO_PASS=y9g48v1VROJ3nQEr
