import React from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import Navbar from "../components/Navbar";
import "./confirm.css";

const Confirm = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const orderId = searchParams.get("orderId");

  return (
    <>
      <Navbar />
      <div className="confirm-container">
        <div className="confirm-box">
          <div className="icon">&#10004;</div>
          <h2>Order Placed Successfully!</h2>
          {orderId ? (
            <p>
              Your order ID is: <strong>{orderId}</strong>
            </p>
          ) : (
            <p className="error">Unable to retrieve Order ID.</p>
          )}
          <p className="call-info">
            You will receive a confirmation call within 5 minutes.
          </p>
          <button onClick={() => navigate("/")} className="home-btn">
            Go to Home
          </button>
        </div>
      </div>
    </>
  );
};

export default Confirm;
