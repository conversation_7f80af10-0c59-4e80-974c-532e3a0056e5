import { NODE_ENV } from "../config/env.js";

const errorMiddleware = (err, req, res, next) => {
  // Log errors for monitoring
  console.error(`[${new Date().toISOString()}] Error:`, {
    path: req.path,
    method: req.method,
    error: err.message,
    ...(NODE_ENV === "development" && { stack: err.stack }),
  });

  const statusCode = err.statusCode || 500;
  const message =
    NODE_ENV === "production" && statusCode === 500
      ? "An internal server error occurred"
      : err.message || "Something went wrong";

  // Mongoose Validation Error
  if (err.name === "ValidationError") {
    const errors = Object.values(err.errors).map((e) => e.message);
    return res.status(400).json({
      success: false,
      status: "fail",
      message: "Validation failed",
      errors,
    });
  }

  // Mongoose Duplicate Key Error
  if (err.code === 11000) {
    return res.status(409).json({
      success: false,
      status: "fail",
      message: "A record with this value already exists",
      fields: NODE_ENV === "production" ? undefined : err.keyValue,
    });
  }

  // JWT Error handling
  if (err.name === "JsonWebTokenError") {
    return res.status(401).json({
      success: false,
      status: "fail",
      message: "Invalid token. Please log in again.",
    });
  }

  // Token expiration error
  if (err.name === "TokenExpiredError") {
    return res.status(401).json({
      success: false,
      status: "fail",
      message: "Your session has expired. Please log in again.",
    });
  }

  // Send default structured error
  res.status(statusCode).json({
    success: false,
    status: statusCode >= 500 ? "error" : "fail",
    message,
    // Only include stack trace in development
    ...(NODE_ENV === "development" && { stack: err.stack }),
  });
};

export default errorMiddleware;
