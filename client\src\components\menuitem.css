*,
*::before,
*::after {
  box-sizing: border-box;
}
* {
  margin: 0;
  padding: 0;
}
html,
body {
  height: 100%;
  width: 100%;
}
html {
  font-size: 62.5%;
}
:root {
  --font-c: #252525;
  --bg-c: #ffffff;
}

img {
  width: 100%;
}
h2 {
  font-size: 1.8rem;
  font-weight: 600;
  line-height: 27px;
  text-transform: capitalize;
}

#item-container {
  height: 139px;
  width: 100%;
  padding: 0.8rem;
  background-color: rgb(249, 246, 246);
  border-radius: 0.8rem;
  display: flex;
  gap: 0.8rem;
}

.item-left {
  flex: 4;
}
.item-left img {
  height: 100%;
  border-bottom-left-radius: 0.8rem;
  border-top-left-radius: 0.8rem;
}
.item-right {
  flex: 6;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.right-top {
  display: flex;
  justify-content: space-between;
}
.right-middle p {
  font-size: 1.2rem;
  font-weight: 400;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.6;
}

.right-bottom {
  display: flex;
  justify-content: space-between;
}

.right-bottom-left h4 {
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 22.2px;
}
.right-bottom-left h6 {
  font-size: 1rem;
  font-weight: 400;
  line-height: 16px;
  text-transform: uppercase;
}

/* Style for the Add button */
.right-bottom-right button {
  font-size: 1rem;
  line-height: 16px;
  text-transform: uppercase;
  font-weight: 400;
  height: 100%;
  padding-inline: 3.2rem;
  background-color: rgba(255, 255, 0, 0.372);
  border: 1px solid yellow;
  border-radius: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.right-bottom-right button:hover {
  background-color: rgba(255, 255, 0, 0.6);
}

/* Counter container */
.counter {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

/* Counter buttons */
.counter button {
  font-size: 1rem;
  line-height: 16px;
  text-transform: uppercase;
  font-weight: 400;
  height: 100%;
  padding-inline: 1rem;
  background-color: rgba(255, 255, 0, 0.372);
  border: 1px solid yellow;
  border-radius: 0.8rem;
  cursor: pointer;
}

.counter button:hover {
  background-color: rgba(255, 255, 0, 0.6);
}

/* Counter number */
.counter span {
  font-size: 1rem;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(145, 143, 143, 0.2) 100%
  );
  border-top: 1px solid #ccc;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
}
.bottom-bar-left {
  display: flex;
  flex-direction: column;
}
.bottom-bar-left span {
  font-size: 1.4rem;
  line-height: 22.4px;
  opacity: 0.7;
  font-weight: 400;
}
.bottom-bar-left .price {
  opacity: 1;
  font-weight: 600;
}

.place-order-btn {
  font-size: 1.4rem;
  line-height: 22.4px;
  font-weight: 600;
  font-family: inter;
  padding: 1.6rem 4rem;
  background-color: #ffdd00;
  border: none;
  border-radius: 0.8rem;
}
