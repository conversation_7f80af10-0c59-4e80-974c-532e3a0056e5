// Production Connection Test Utility
import { apiUtils } from '../services/api.js';
import { validateEnvironment, checkApiConnection } from './env.js';

/**
 * Comprehensive connection and configuration test
 * @returns {Promise<Object>} Test results
 */
export const runConnectionTests = async () => {
  const results = {
    environment: null,
    apiConnection: null,
    cors: null,
    endpoints: null,
    overall: false,
    timestamp: new Date().toISOString()
  };

  try {
    // 1. Environment validation
    console.log('🔍 Testing environment configuration...');
    const envValidation = validateEnvironment();
    results.environment = {
      passed: envValidation.isValid,
      issues: [
        ...envValidation.missing.map(v => `Missing variable: ${v}`),
        ...(envValidation.urlError ? [envValidation.urlError] : [])
      ]
    };

    // 2. API connection test
    console.log('🌐 Testing API connection...');
    const apiReachable = await checkApiConnection();
    results.apiConnection = {
      passed: apiReachable,
      issues: apiReachable ? [] : ['API server is not reachable']
    };

    // 3. CORS test (if API is reachable)
    if (apiReachable) {
      console.log('🔒 Testing CORS configuration...');
      const corsTest = await testCorsConfiguration();
      results.cors = corsTest;
    } else {
      results.cors = {
        passed: false,
        issues: ['Cannot test CORS - API not reachable'],
        skipped: true
      };
    }

    // 4. Endpoint tests (if API is reachable)
    if (apiReachable) {
      console.log('🎯 Testing API endpoints...');
      const endpointTests = await testApiEndpoints();
      results.endpoints = endpointTests;
    } else {
      results.endpoints = {
        passed: false,
        issues: ['Cannot test endpoints - API not reachable'],
        skipped: true
      };
    }

    // Overall result
    results.overall = results.environment.passed && 
                     results.apiConnection.passed && 
                     results.cors.passed && 
                     results.endpoints.passed;

    return results;
  } catch (error) {
    console.error('Connection test failed:', error);
    return {
      ...results,
      error: error.message,
      overall: false
    };
  }
};

/**
 * Test CORS configuration
 * @returns {Promise<Object>} CORS test results
 */
const testCorsConfiguration = async () => {
  try {
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Test credentials
    });

    if (response.ok) {
      return {
        passed: true,
        issues: []
      };
    } else {
      return {
        passed: false,
        issues: [`CORS test failed with status: ${response.status}`]
      };
    }
  } catch (error) {
    if (error.name === 'TypeError' && error.message.includes('CORS')) {
      return {
        passed: false,
        issues: ['CORS policy blocking requests - check server CORS configuration']
      };
    }
    return {
      passed: false,
      issues: [`CORS test error: ${error.message}`]
    };
  }
};

/**
 * Test critical API endpoints
 * @returns {Promise<Object>} Endpoint test results
 */
const testApiEndpoints = async () => {
  const endpoints = [
    { name: 'Health Check', path: '/health', method: 'GET' },
    { name: 'Menu Items', path: '/v1/api/menu-item/', method: 'GET' },
  ];

  const results = [];
  let allPassed = true;

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}${endpoint.path}`, {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const passed = response.ok;
      if (!passed) allPassed = false;

      results.push({
        name: endpoint.name,
        path: endpoint.path,
        passed,
        status: response.status,
        issues: passed ? [] : [`HTTP ${response.status}: ${response.statusText}`]
      });
    } catch (error) {
      allPassed = false;
      results.push({
        name: endpoint.name,
        path: endpoint.path,
        passed: false,
        issues: [`Request failed: ${error.message}`]
      });
    }
  }

  return {
    passed: allPassed,
    endpoints: results,
    issues: allPassed ? [] : ['Some endpoints failed - check server configuration']
  };
};

/**
 * Display test results in console
 * @param {Object} results - Test results from runConnectionTests
 */
export const displayTestResults = (results) => {
  console.log('\n🧪 Production Connection Test Results');
  console.log('=====================================');
  
  // Environment
  console.log(`\n🔧 Environment: ${results.environment.passed ? '✅ PASS' : '❌ FAIL'}`);
  if (results.environment.issues.length > 0) {
    results.environment.issues.forEach(issue => console.log(`   ⚠️ ${issue}`));
  }

  // API Connection
  console.log(`\n🌐 API Connection: ${results.apiConnection.passed ? '✅ PASS' : '❌ FAIL'}`);
  if (results.apiConnection.issues.length > 0) {
    results.apiConnection.issues.forEach(issue => console.log(`   ⚠️ ${issue}`));
  }

  // CORS
  if (!results.cors.skipped) {
    console.log(`\n🔒 CORS: ${results.cors.passed ? '✅ PASS' : '❌ FAIL'}`);
    if (results.cors.issues.length > 0) {
      results.cors.issues.forEach(issue => console.log(`   ⚠️ ${issue}`));
    }
  }

  // Endpoints
  if (!results.endpoints.skipped) {
    console.log(`\n🎯 Endpoints: ${results.endpoints.passed ? '✅ PASS' : '❌ FAIL'}`);
    if (results.endpoints.endpoints) {
      results.endpoints.endpoints.forEach(ep => {
        console.log(`   ${ep.passed ? '✅' : '❌'} ${ep.name} (${ep.path})`);
        if (ep.issues.length > 0) {
          ep.issues.forEach(issue => console.log(`      ⚠️ ${issue}`));
        }
      });
    }
  }

  // Overall
  console.log(`\n🎯 Overall Result: ${results.overall ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (!results.overall) {
    console.log('\n💡 Troubleshooting Tips:');
    console.log('   1. Check that your backend server is running');
    console.log('   2. Verify CORS_ORIGIN in server matches your client domain');
    console.log('   3. Ensure VITE_API_BASE_URL points to correct server');
    console.log('   4. Check network connectivity and firewall settings');
  }

  console.log('\n=====================================\n');
};

/**
 * Run connection tests and display results (for development)
 */
export const testProductionConnection = async () => {
  if (import.meta.env.VITE_NODE_ENV === 'development') {
    console.log('🚀 Running production connection tests...');
    const results = await runConnectionTests();
    displayTestResults(results);
    return results;
  } else {
    console.log('Connection tests are only available in development mode');
    return null;
  }
};
