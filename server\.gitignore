# Node.js
node_modules/

npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Logs
logs/
*.log
*.pid
*.seed
*.pid.lock

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov/

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt/

# Bower dependency directory (if using Bower)
bower_components/

# dotenv environment files
.env
.env.test
.env.local
.env.*.local

# Parcel-bundler cache
.cache/

# Next.js build output
.next/

# Nuxt.js build output
.nuxt/

# Gatsby files
.cache/
public/

# Vuepress build output
.vuepress/dist/

# Storybook build outputs
.out/
.storybook-out/

# TypeScript
*.tsbuildinfo

# Optional npm cache directory
.npm/

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Mac system files
.DS_Store

# Windows system files
Thumbs.db

# VSCode
.vscode/
