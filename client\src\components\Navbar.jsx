import React, { useState } from "react";
import { assets } from "../assets/assets.js";
import LazyImage from "./LazyImage";
import "./navbar.css";

const Navbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const toggleMenu = () => {
    setMenuOpen((prev) => !prev);
  };
  return (
    <header id="header">
      <LazyImage
        id="navlogo"
        src={assets.logo}
        alt="website nav logo"
        className="navbar-logo"
      />
      <div className="container">
       <i id="menu-icon" className="ri-menu-3-line"></i>
      </div>
    </header>
  );
};

export default Navbar;
