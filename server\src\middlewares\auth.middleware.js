import jwt from "jsonwebtoken";
import { JWT_SECRET } from "../config/env.js";
import { Admin } from "../models/admin.model.js";

export const authorizeAdmin = async (req, res, next) => {
  const token = req.cookies?.accessToken;
  if (!token) {
    return res.status(401).json({
      success: false,
      message: "Unauthorized",
    });
  }
  try {
    const decoded = jwt.verify(token, JWT_SECRET);

    const user = await Admin.findById(decoded.userId).select(
      "-createdAt -updatedAt -__v"
    );

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized",
      });
    }

    if (user.role !== "admin") {
      return res.status(401).json({
        success: false,
        message: "Unauthorized",
      });
    }

    req.user = user;
    next();
  } catch (error) {
    res
      .status(401)
      .json({ success: false, message: "Unauthorized", error: error.message });
  }
};

// export const authorizeUser = async (req, res, next) => {
//   const token = req.cookies?.accessToken;
//   if (!token) {
//     return res.status(401).json({
//       success: false,
//       message: "Unauthorized",
//     });
//   }
//   try {
//     const decoded = jwt.verify(token, JWT_SECRET);

//     const admin = await Admin.findById(decoded.userId);
//     if (admin) {
//       if (admin.role === "admin" && admin.isVerified) {
//         req.user = admin;
//         return next();
//       }
//     }

//     const user = await User.findById(decoded.userId).select(
//       "-createdAt -updatedAt -__v -cart -address -order_history"
//     );
//     if (!user) {
//       return res.status(401).json({
//         success: false,
//         message: "Unauthorized",
//       });
//     }

//     req.user = user;
//     next();
//   } catch (error) {
//     res
//       .status(401)
//       .json({ success: false, message: "Unauthorized", error: error.message });
//   }
// };
