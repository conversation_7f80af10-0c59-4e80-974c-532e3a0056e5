function nt(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ce={exports:{}},S={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var be;function at(){if(be)return S;be=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),u=Symbol.for("react.consumer"),s=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),n=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),g=Symbol.iterator;function y(i){return i===null||typeof i!="object"?null:(i=g&&i[g]||i["@@iterator"],typeof i=="function"?i:null)}var R={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,v={};function E(i,p,w){this.props=i,this.context=p,this.refs=v,this.updater=w||R}E.prototype.isReactComponent={},E.prototype.setState=function(i,p){if(typeof i!="object"&&typeof i!="function"&&i!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,i,p,"setState")},E.prototype.forceUpdate=function(i){this.updater.enqueueForceUpdate(this,i,"forceUpdate")};function h(){}h.prototype=E.prototype;function _(i,p,w){this.props=i,this.context=p,this.refs=v,this.updater=w||R}var b=_.prototype=new h;b.constructor=_,m(b,E.prototype),b.isPureReactComponent=!0;var k=Array.isArray,x={H:null,A:null,T:null,S:null,V:null},F=Object.prototype.hasOwnProperty;function D(i,p,w,C,T,O){return w=O.ref,{$$typeof:e,type:i,key:p,ref:w!==void 0?w:null,props:O}}function B(i,p){return D(i.type,p,void 0,void 0,void 0,i.props)}function A(i){return typeof i=="object"&&i!==null&&i.$$typeof===e}function H(i){var p={"=":"=0",":":"=2"};return"$"+i.replace(/[=:]/g,function(w){return p[w]})}var J=/\/+/g;function se(i,p){return typeof i=="object"&&i!==null&&i.key!=null?H(""+i.key):p.toString(36)}function _e(){}function Ze(i){switch(i.status){case"fulfilled":return i.value;case"rejected":throw i.reason;default:switch(typeof i.status=="string"?i.then(_e,_e):(i.status="pending",i.then(function(p){i.status==="pending"&&(i.status="fulfilled",i.value=p)},function(p){i.status==="pending"&&(i.status="rejected",i.reason=p)})),i.status){case"fulfilled":return i.value;case"rejected":throw i.reason}}throw i}function q(i,p,w,C,T){var O=typeof i;(O==="undefined"||O==="boolean")&&(i=null);var P=!1;if(i===null)P=!0;else switch(O){case"bigint":case"string":case"number":P=!0;break;case"object":switch(i.$$typeof){case e:case t:P=!0;break;case d:return P=i._init,q(P(i._payload),p,w,C,T)}}if(P)return T=T(i),P=C===""?"."+se(i,0):C,k(T)?(w="",P!=null&&(w=P.replace(J,"$&/")+"/"),q(T,p,w,"",function(rt){return rt})):T!=null&&(A(T)&&(T=B(T,w+(T.key==null||i&&i.key===T.key?"":(""+T.key).replace(J,"$&/")+"/")+P)),p.push(T)),1;P=0;var Y=C===""?".":C+":";if(k(i))for(var $=0;$<i.length;$++)C=i[$],O=Y+se(C,$),P+=q(C,p,w,O,T);else if($=y(i),typeof $=="function")for(i=$.call(i),$=0;!(C=i.next()).done;)C=C.value,O=Y+se(C,$++),P+=q(C,p,w,O,T);else if(O==="object"){if(typeof i.then=="function")return q(Ze(i),p,w,C,T);throw p=String(i),Error("Objects are not valid as a React child (found: "+(p==="[object Object]"?"object with keys {"+Object.keys(i).join(", ")+"}":p)+"). If you meant to render a collection of children, use an array instead.")}return P}function re(i,p,w){if(i==null)return i;var C=[],T=0;return q(i,C,"","",function(O){return p.call(w,O,T++)}),C}function et(i){if(i._status===-1){var p=i._result;p=p(),p.then(function(w){(i._status===0||i._status===-1)&&(i._status=1,i._result=w)},function(w){(i._status===0||i._status===-1)&&(i._status=2,i._result=w)}),i._status===-1&&(i._status=0,i._result=p)}if(i._status===1)return i._result.default;throw i._result}var Pe=typeof reportError=="function"?reportError:function(i){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var p=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof i=="object"&&i!==null&&typeof i.message=="string"?String(i.message):String(i),error:i});if(!window.dispatchEvent(p))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",i);return}console.error(i)};function tt(){}return S.Children={map:re,forEach:function(i,p,w){re(i,function(){p.apply(this,arguments)},w)},count:function(i){var p=0;return re(i,function(){p++}),p},toArray:function(i){return re(i,function(p){return p})||[]},only:function(i){if(!A(i))throw Error("React.Children.only expected to receive a single React element child.");return i}},S.Component=E,S.Fragment=r,S.Profiler=a,S.PureComponent=_,S.StrictMode=o,S.Suspense=l,S.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=x,S.__COMPILER_RUNTIME={__proto__:null,c:function(i){return x.H.useMemoCache(i)}},S.cache=function(i){return function(){return i.apply(null,arguments)}},S.cloneElement=function(i,p,w){if(i==null)throw Error("The argument must be a React element, but you passed "+i+".");var C=m({},i.props),T=i.key,O=void 0;if(p!=null)for(P in p.ref!==void 0&&(O=void 0),p.key!==void 0&&(T=""+p.key),p)!F.call(p,P)||P==="key"||P==="__self"||P==="__source"||P==="ref"&&p.ref===void 0||(C[P]=p[P]);var P=arguments.length-2;if(P===1)C.children=w;else if(1<P){for(var Y=Array(P),$=0;$<P;$++)Y[$]=arguments[$+2];C.children=Y}return D(i.type,T,void 0,void 0,O,C)},S.createContext=function(i){return i={$$typeof:s,_currentValue:i,_currentValue2:i,_threadCount:0,Provider:null,Consumer:null},i.Provider=i,i.Consumer={$$typeof:u,_context:i},i},S.createElement=function(i,p,w){var C,T={},O=null;if(p!=null)for(C in p.key!==void 0&&(O=""+p.key),p)F.call(p,C)&&C!=="key"&&C!=="__self"&&C!=="__source"&&(T[C]=p[C]);var P=arguments.length-2;if(P===1)T.children=w;else if(1<P){for(var Y=Array(P),$=0;$<P;$++)Y[$]=arguments[$+2];T.children=Y}if(i&&i.defaultProps)for(C in P=i.defaultProps,P)T[C]===void 0&&(T[C]=P[C]);return D(i,O,void 0,void 0,null,T)},S.createRef=function(){return{current:null}},S.forwardRef=function(i){return{$$typeof:f,render:i}},S.isValidElement=A,S.lazy=function(i){return{$$typeof:d,_payload:{_status:-1,_result:i},_init:et}},S.memo=function(i,p){return{$$typeof:n,type:i,compare:p===void 0?null:p}},S.startTransition=function(i){var p=x.T,w={};x.T=w;try{var C=i(),T=x.S;T!==null&&T(w,C),typeof C=="object"&&C!==null&&typeof C.then=="function"&&C.then(tt,Pe)}catch(O){Pe(O)}finally{x.T=p}},S.unstable_useCacheRefresh=function(){return x.H.useCacheRefresh()},S.use=function(i){return x.H.use(i)},S.useActionState=function(i,p,w){return x.H.useActionState(i,p,w)},S.useCallback=function(i,p){return x.H.useCallback(i,p)},S.useContext=function(i){return x.H.useContext(i)},S.useDebugValue=function(){},S.useDeferredValue=function(i,p){return x.H.useDeferredValue(i,p)},S.useEffect=function(i,p,w){var C=x.H;if(typeof w=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return C.useEffect(i,p)},S.useId=function(){return x.H.useId()},S.useImperativeHandle=function(i,p,w){return x.H.useImperativeHandle(i,p,w)},S.useInsertionEffect=function(i,p){return x.H.useInsertionEffect(i,p)},S.useLayoutEffect=function(i,p){return x.H.useLayoutEffect(i,p)},S.useMemo=function(i,p){return x.H.useMemo(i,p)},S.useOptimistic=function(i,p){return x.H.useOptimistic(i,p)},S.useReducer=function(i,p,w){return x.H.useReducer(i,p,w)},S.useRef=function(i){return x.H.useRef(i)},S.useState=function(i){return x.H.useState(i)},S.useSyncExternalStore=function(i,p,w){return x.H.useSyncExternalStore(i,p,w)},S.useTransition=function(){return x.H.useTransition()},S.version="19.1.0",S}var Te;function Me(){return Te||(Te=1,ce.exports=at()),ce.exports}var c=Me();const Or=nt(c);var fe={exports:{}},N={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Le;function ot(){if(Le)return N;Le=1;var e=Me();function t(l){var n="https://react.dev/errors/"+l;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var d=2;d<arguments.length;d++)n+="&args[]="+encodeURIComponent(arguments[d])}return"Minified React error #"+l+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var o={d:{f:r,r:function(){throw Error(t(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},a=Symbol.for("react.portal");function u(l,n,d){var g=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:a,key:g==null?null:""+g,children:l,containerInfo:n,implementation:d}}var s=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function f(l,n){if(l==="font")return"";if(typeof n=="string")return n==="use-credentials"?n:""}return N.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,N.createPortal=function(l,n){var d=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!n||n.nodeType!==1&&n.nodeType!==9&&n.nodeType!==11)throw Error(t(299));return u(l,n,null,d)},N.flushSync=function(l){var n=s.T,d=o.p;try{if(s.T=null,o.p=2,l)return l()}finally{s.T=n,o.p=d,o.d.f()}},N.preconnect=function(l,n){typeof l=="string"&&(n?(n=n.crossOrigin,n=typeof n=="string"?n==="use-credentials"?n:"":void 0):n=null,o.d.C(l,n))},N.prefetchDNS=function(l){typeof l=="string"&&o.d.D(l)},N.preinit=function(l,n){if(typeof l=="string"&&n&&typeof n.as=="string"){var d=n.as,g=f(d,n.crossOrigin),y=typeof n.integrity=="string"?n.integrity:void 0,R=typeof n.fetchPriority=="string"?n.fetchPriority:void 0;d==="style"?o.d.S(l,typeof n.precedence=="string"?n.precedence:void 0,{crossOrigin:g,integrity:y,fetchPriority:R}):d==="script"&&o.d.X(l,{crossOrigin:g,integrity:y,fetchPriority:R,nonce:typeof n.nonce=="string"?n.nonce:void 0})}},N.preinitModule=function(l,n){if(typeof l=="string")if(typeof n=="object"&&n!==null){if(n.as==null||n.as==="script"){var d=f(n.as,n.crossOrigin);o.d.M(l,{crossOrigin:d,integrity:typeof n.integrity=="string"?n.integrity:void 0,nonce:typeof n.nonce=="string"?n.nonce:void 0})}}else n==null&&o.d.M(l)},N.preload=function(l,n){if(typeof l=="string"&&typeof n=="object"&&n!==null&&typeof n.as=="string"){var d=n.as,g=f(d,n.crossOrigin);o.d.L(l,d,{crossOrigin:g,integrity:typeof n.integrity=="string"?n.integrity:void 0,nonce:typeof n.nonce=="string"?n.nonce:void 0,type:typeof n.type=="string"?n.type:void 0,fetchPriority:typeof n.fetchPriority=="string"?n.fetchPriority:void 0,referrerPolicy:typeof n.referrerPolicy=="string"?n.referrerPolicy:void 0,imageSrcSet:typeof n.imageSrcSet=="string"?n.imageSrcSet:void 0,imageSizes:typeof n.imageSizes=="string"?n.imageSizes:void 0,media:typeof n.media=="string"?n.media:void 0})}},N.preloadModule=function(l,n){if(typeof l=="string")if(n){var d=f(n.as,n.crossOrigin);o.d.m(l,{as:typeof n.as=="string"&&n.as!=="script"?n.as:void 0,crossOrigin:d,integrity:typeof n.integrity=="string"?n.integrity:void 0})}else o.d.m(l)},N.requestFormReset=function(l){o.d.r(l)},N.unstable_batchedUpdates=function(l,n){return l(n)},N.useFormState=function(l,n,d){return s.H.useFormState(l,n,d)},N.useFormStatus=function(){return s.H.useHostTransitionStatus()},N.version="19.1.0",N}var Oe;function kr(){if(Oe)return fe.exports;Oe=1;function e(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}return e(),fe.exports=ot(),fe.exports}var X={},ke;function it(){if(ke)return X;ke=1,Object.defineProperty(X,"__esModule",{value:!0}),X.parse=s,X.serialize=n;const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,u=(()=>{const y=function(){};return y.prototype=Object.create(null),y})();function s(y,R){const m=new u,v=y.length;if(v<2)return m;const E=R?.decode||d;let h=0;do{const _=y.indexOf("=",h);if(_===-1)break;const b=y.indexOf(";",h),k=b===-1?v:b;if(_>k){h=y.lastIndexOf(";",_-1)+1;continue}const x=f(y,h,_),F=l(y,_,x),D=y.slice(x,F);if(m[D]===void 0){let B=f(y,_+1,k),A=l(y,k,B);const H=E(y.slice(B,A));m[D]=H}h=k+1}while(h<v);return m}function f(y,R,m){do{const v=y.charCodeAt(R);if(v!==32&&v!==9)return R}while(++R<m);return m}function l(y,R,m){for(;R>m;){const v=y.charCodeAt(--R);if(v!==32&&v!==9)return R+1}return m}function n(y,R,m){const v=m?.encode||encodeURIComponent;if(!e.test(y))throw new TypeError(`argument name is invalid: ${y}`);const E=v(R);if(!t.test(E))throw new TypeError(`argument val is invalid: ${R}`);let h=y+"="+E;if(!m)return h;if(m.maxAge!==void 0){if(!Number.isInteger(m.maxAge))throw new TypeError(`option maxAge is invalid: ${m.maxAge}`);h+="; Max-Age="+m.maxAge}if(m.domain){if(!r.test(m.domain))throw new TypeError(`option domain is invalid: ${m.domain}`);h+="; Domain="+m.domain}if(m.path){if(!o.test(m.path))throw new TypeError(`option path is invalid: ${m.path}`);h+="; Path="+m.path}if(m.expires){if(!g(m.expires)||!Number.isFinite(m.expires.valueOf()))throw new TypeError(`option expires is invalid: ${m.expires}`);h+="; Expires="+m.expires.toUTCString()}if(m.httpOnly&&(h+="; HttpOnly"),m.secure&&(h+="; Secure"),m.partitioned&&(h+="; Partitioned"),m.priority)switch(typeof m.priority=="string"?m.priority.toLowerCase():void 0){case"low":h+="; Priority=Low";break;case"medium":h+="; Priority=Medium";break;case"high":h+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${m.priority}`)}if(m.sameSite)switch(typeof m.sameSite=="string"?m.sameSite.toLowerCase():m.sameSite){case!0:case"strict":h+="; SameSite=Strict";break;case"lax":h+="; SameSite=Lax";break;case"none":h+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${m.sameSite}`)}return h}function d(y){if(y.indexOf("%")===-1)return y;try{return decodeURIComponent(y)}catch{return y}}function g(y){return a.call(y)==="[object Date]"}return X}it();var $e="popstate";function ut(e={}){function t(o,a){let{pathname:u,search:s,hash:f}=o.location;return pe("",{pathname:u,search:s,hash:f},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(o,a){return typeof a=="string"?a:Z(a)}return st(t,r,null,e)}function L(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function I(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function lt(){return Math.random().toString(36).substring(2,10)}function Ae(e,t){return{usr:e.state,key:e.key,idx:t}}function pe(e,t,r=null,o){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?V(t):t,state:r,key:t&&t.key||o||lt()}}function Z({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function V(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let o=e.indexOf("?");o>=0&&(t.search=e.substring(o),e=e.substring(0,o)),e&&(t.pathname=e)}return t}function st(e,t,r,o={}){let{window:a=document.defaultView,v5Compat:u=!1}=o,s=a.history,f="POP",l=null,n=d();n==null&&(n=0,s.replaceState({...s.state,idx:n},""));function d(){return(s.state||{idx:null}).idx}function g(){f="POP";let E=d(),h=E==null?null:E-n;n=E,l&&l({action:f,location:v.location,delta:h})}function y(E,h){f="PUSH";let _=pe(v.location,E,h);n=d()+1;let b=Ae(_,n),k=v.createHref(_);try{s.pushState(b,"",k)}catch(x){if(x instanceof DOMException&&x.name==="DataCloneError")throw x;a.location.assign(k)}u&&l&&l({action:f,location:v.location,delta:1})}function R(E,h){f="REPLACE";let _=pe(v.location,E,h);n=d();let b=Ae(_,n),k=v.createHref(_);s.replaceState(b,"",k),u&&l&&l({action:f,location:v.location,delta:0})}function m(E){return ct(E)}let v={get action(){return f},get location(){return e(a,s)},listen(E){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener($e,g),l=E,()=>{a.removeEventListener($e,g),l=null}},createHref(E){return t(a,E)},createURL:m,encodeLocation(E){let h=m(E);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:y,replace:R,go(E){return s.go(E)}};return v}function ct(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),L(r,"No window.location.(origin|href) available to create URL");let o=typeof e=="string"?e:Z(e);return o=o.replace(/ $/,"%20"),!t&&o.startsWith("//")&&(o=r+o),new URL(o,r)}function He(e,t,r="/"){return ft(e,t,r,!1)}function ft(e,t,r,o){let a=typeof t=="string"?V(t):t,u=W(a.pathname||"/",r);if(u==null)return null;let s=Ue(e);dt(s);let f=null;for(let l=0;f==null&&l<s.length;++l){let n=St(u);f=wt(s[l],n,o)}return f}function Ue(e,t=[],r=[],o=""){let a=(u,s,f)=>{let l={relativePath:f===void 0?u.path||"":f,caseSensitive:u.caseSensitive===!0,childrenIndex:s,route:u};l.relativePath.startsWith("/")&&(L(l.relativePath.startsWith(o),`Absolute route path "${l.relativePath}" nested under path "${o}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),l.relativePath=l.relativePath.slice(o.length));let n=j([o,l.relativePath]),d=r.concat(l);u.children&&u.children.length>0&&(L(u.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${n}".`),Ue(u.children,t,d,n)),!(u.path==null&&!u.index)&&t.push({path:n,score:Et(n,u.index),routesMeta:d})};return e.forEach((u,s)=>{if(u.path===""||!u.path?.includes("?"))a(u,s);else for(let f of Fe(u.path))a(u,s,f)}),t}function Fe(e){let t=e.split("/");if(t.length===0)return[];let[r,...o]=t,a=r.endsWith("?"),u=r.replace(/\?$/,"");if(o.length===0)return a?[u,""]:[u];let s=Fe(o.join("/")),f=[];return f.push(...s.map(l=>l===""?u:[u,l].join("/"))),a&&f.push(...s),f.map(l=>e.startsWith("/")&&l===""?"/":l)}function dt(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Rt(t.routesMeta.map(o=>o.childrenIndex),r.routesMeta.map(o=>o.childrenIndex)))}var ht=/^:[\w-]+$/,pt=3,mt=2,yt=1,gt=10,vt=-2,Ne=e=>e==="*";function Et(e,t){let r=e.split("/"),o=r.length;return r.some(Ne)&&(o+=vt),t&&(o+=mt),r.filter(a=>!Ne(a)).reduce((a,u)=>a+(ht.test(u)?pt:u===""?yt:gt),o)}function Rt(e,t){return e.length===t.length&&e.slice(0,-1).every((o,a)=>o===t[a])?e[e.length-1]-t[t.length-1]:0}function wt(e,t,r=!1){let{routesMeta:o}=e,a={},u="/",s=[];for(let f=0;f<o.length;++f){let l=o[f],n=f===o.length-1,d=u==="/"?t:t.slice(u.length)||"/",g=ie({path:l.relativePath,caseSensitive:l.caseSensitive,end:n},d),y=l.route;if(!g&&n&&r&&!o[o.length-1].route.index&&(g=ie({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},d)),!g)return null;Object.assign(a,g.params),s.push({params:a,pathname:j([u,g.pathname]),pathnameBase:bt(j([u,g.pathnameBase])),route:y}),g.pathnameBase!=="/"&&(u=j([u,g.pathnameBase]))}return s}function ie(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,o]=Ct(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let u=a[0],s=u.replace(/(.)\/+$/,"$1"),f=a.slice(1);return{params:o.reduce((n,{paramName:d,isOptional:g},y)=>{if(d==="*"){let m=f[y]||"";s=u.slice(0,u.length-m.length).replace(/(.)\/+$/,"$1")}const R=f[y];return g&&!R?n[d]=void 0:n[d]=(R||"").replace(/%2F/g,"/"),n},{}),pathname:u,pathnameBase:s,pattern:e}}function Ct(e,t=!1,r=!0){I(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let o=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,f,l)=>(o.push({paramName:f,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(o.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),o]}function St(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return I(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function W(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,o=e.charAt(r);return o&&o!=="/"?null:e.slice(r)||"/"}function xt(e,t="/"){let{pathname:r,search:o="",hash:a=""}=typeof e=="string"?V(e):e;return{pathname:r?r.startsWith("/")?r:_t(r,t):t,search:Tt(o),hash:Lt(a)}}function _t(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function de(e,t,r,o){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(o)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Pt(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function ge(e){let t=Pt(e);return t.map((r,o)=>o===t.length-1?r.pathname:r.pathnameBase)}function ve(e,t,r,o=!1){let a;typeof e=="string"?a=V(e):(a={...e},L(!a.pathname||!a.pathname.includes("?"),de("?","pathname","search",a)),L(!a.pathname||!a.pathname.includes("#"),de("#","pathname","hash",a)),L(!a.search||!a.search.includes("#"),de("#","search","hash",a)));let u=e===""||a.pathname==="",s=u?"/":a.pathname,f;if(s==null)f=r;else{let g=t.length-1;if(!o&&s.startsWith("..")){let y=s.split("/");for(;y[0]==="..";)y.shift(),g-=1;a.pathname=y.join("/")}f=g>=0?t[g]:"/"}let l=xt(a,f),n=s&&s!=="/"&&s.endsWith("/"),d=(u||s===".")&&r.endsWith("/");return!l.pathname.endsWith("/")&&(n||d)&&(l.pathname+="/"),l}var j=e=>e.join("/").replace(/\/\/+/g,"/"),bt=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Tt=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Lt=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ot(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Be=["POST","PUT","PATCH","DELETE"];new Set(Be);var kt=["GET",...Be];new Set(kt);var K=c.createContext(null);K.displayName="DataRouter";var ue=c.createContext(null);ue.displayName="DataRouterState";var je=c.createContext({isTransitioning:!1});je.displayName="ViewTransition";var $t=c.createContext(new Map);$t.displayName="Fetchers";var At=c.createContext(null);At.displayName="Await";var M=c.createContext(null);M.displayName="Navigation";var ee=c.createContext(null);ee.displayName="Location";var U=c.createContext({outlet:null,matches:[],isDataRoute:!1});U.displayName="Route";var Ee=c.createContext(null);Ee.displayName="RouteError";function Nt(e,{relative:t}={}){L(G(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:o}=c.useContext(M),{hash:a,pathname:u,search:s}=te(e,{relative:t}),f=u;return r!=="/"&&(f=u==="/"?r:j([r,u])),o.createHref({pathname:f,search:s,hash:a})}function G(){return c.useContext(ee)!=null}function z(){return L(G(),"useLocation() may be used only in the context of a <Router> component."),c.useContext(ee).location}var We="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ze(e){c.useContext(M).static||c.useLayoutEffect(e)}function Re(){let{isDataRoute:e}=c.useContext(U);return e?Vt():Dt()}function Dt(){L(G(),"useNavigate() may be used only in the context of a <Router> component.");let e=c.useContext(K),{basename:t,navigator:r}=c.useContext(M),{matches:o}=c.useContext(U),{pathname:a}=z(),u=JSON.stringify(ge(o)),s=c.useRef(!1);return ze(()=>{s.current=!0}),c.useCallback((l,n={})=>{if(I(s.current,We),!s.current)return;if(typeof l=="number"){r.go(l);return}let d=ve(l,JSON.parse(u),a,n.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:j([t,d.pathname])),(n.replace?r.replace:r.push)(d,n.state,n)},[t,r,u,a,e])}c.createContext(null);function te(e,{relative:t}={}){let{matches:r}=c.useContext(U),{pathname:o}=z(),a=JSON.stringify(ge(r));return c.useMemo(()=>ve(e,JSON.parse(a),o,t==="path"),[e,a,o,t])}function It(e,t){return Ye(e,t)}function Ye(e,t,r,o){L(G(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=c.useContext(M),{matches:u}=c.useContext(U),s=u[u.length-1],f=s?s.params:{},l=s?s.pathname:"/",n=s?s.pathnameBase:"/",d=s&&s.route;{let h=d&&d.path||"";qe(l,!d||h.endsWith("*")||h.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${l}" (under <Route path="${h}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${h}"> to <Route path="${h==="/"?"*":`${h}/*`}">.`)}let g=z(),y;if(t){let h=typeof t=="string"?V(t):t;L(n==="/"||h.pathname?.startsWith(n),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${n}" but pathname "${h.pathname}" was given in the \`location\` prop.`),y=h}else y=g;let R=y.pathname||"/",m=R;if(n!=="/"){let h=n.replace(/^\//,"").split("/");m="/"+R.replace(/^\//,"").split("/").slice(h.length).join("/")}let v=He(e,{pathname:m});I(d||v!=null,`No routes matched location "${y.pathname}${y.search}${y.hash}" `),I(v==null||v[v.length-1].route.element!==void 0||v[v.length-1].route.Component!==void 0||v[v.length-1].route.lazy!==void 0,`Matched leaf route at location "${y.pathname}${y.search}${y.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let E=Bt(v&&v.map(h=>Object.assign({},h,{params:Object.assign({},f,h.params),pathname:j([n,a.encodeLocation?a.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?n:j([n,a.encodeLocation?a.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),u,r,o);return t&&E?c.createElement(ee.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...y},navigationType:"POP"}},E):E}function Mt(){let e=qt(),t=Ot(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,o="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:o},u={padding:"2px 4px",backgroundColor:o},s=null;return console.error("Error handled by React Router default ErrorBoundary:",e),s=c.createElement(c.Fragment,null,c.createElement("p",null,"💿 Hey developer 👋"),c.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",c.createElement("code",{style:u},"ErrorBoundary")," or"," ",c.createElement("code",{style:u},"errorElement")," prop on your route.")),c.createElement(c.Fragment,null,c.createElement("h2",null,"Unexpected Application Error!"),c.createElement("h3",{style:{fontStyle:"italic"}},t),r?c.createElement("pre",{style:a},r):null,s)}var Ht=c.createElement(Mt,null),Ut=class extends c.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?c.createElement(U.Provider,{value:this.props.routeContext},c.createElement(Ee.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Ft({routeContext:e,match:t,children:r}){let o=c.useContext(K);return o&&o.static&&o.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=t.route.id),c.createElement(U.Provider,{value:e},r)}function Bt(e,t=[],r=null,o=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let a=e,u=r?.errors;if(u!=null){let l=a.findIndex(n=>n.route.id&&u?.[n.route.id]!==void 0);L(l>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(u).join(",")}`),a=a.slice(0,Math.min(a.length,l+1))}let s=!1,f=-1;if(r)for(let l=0;l<a.length;l++){let n=a[l];if((n.route.HydrateFallback||n.route.hydrateFallbackElement)&&(f=l),n.route.id){let{loaderData:d,errors:g}=r,y=n.route.loader&&!d.hasOwnProperty(n.route.id)&&(!g||g[n.route.id]===void 0);if(n.route.lazy||y){s=!0,f>=0?a=a.slice(0,f+1):a=[a[0]];break}}}return a.reduceRight((l,n,d)=>{let g,y=!1,R=null,m=null;r&&(g=u&&n.route.id?u[n.route.id]:void 0,R=n.route.errorElement||Ht,s&&(f<0&&d===0?(qe("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),y=!0,m=null):f===d&&(y=!0,m=n.route.hydrateFallbackElement||null)));let v=t.concat(a.slice(0,d+1)),E=()=>{let h;return g?h=R:y?h=m:n.route.Component?h=c.createElement(n.route.Component,null):n.route.element?h=n.route.element:h=l,c.createElement(Ft,{match:n,routeContext:{outlet:l,matches:v,isDataRoute:r!=null},children:h})};return r&&(n.route.ErrorBoundary||n.route.errorElement||d===0)?c.createElement(Ut,{location:r.location,revalidation:r.revalidation,component:R,error:g,children:E(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):E()},null)}function we(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function jt(e){let t=c.useContext(K);return L(t,we(e)),t}function Wt(e){let t=c.useContext(ue);return L(t,we(e)),t}function zt(e){let t=c.useContext(U);return L(t,we(e)),t}function Ce(e){let t=zt(e),r=t.matches[t.matches.length-1];return L(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function Yt(){return Ce("useRouteId")}function qt(){let e=c.useContext(Ee),t=Wt("useRouteError"),r=Ce("useRouteError");return e!==void 0?e:t.errors?.[r]}function Vt(){let{router:e}=jt("useNavigate"),t=Ce("useNavigate"),r=c.useRef(!1);return ze(()=>{r.current=!0}),c.useCallback(async(a,u={})=>{I(r.current,We),r.current&&(typeof a=="number"?e.navigate(a):await e.navigate(a,{fromRouteId:t,...u}))},[e,t])}var De={};function qe(e,t,r){!t&&!De[e]&&(De[e]=!0,I(!1,r))}c.memo(Kt);function Kt({routes:e,future:t,state:r}){return Ye(e,void 0,r,t)}function $r({to:e,replace:t,state:r,relative:o}){L(G(),"<Navigate> may be used only in the context of a <Router> component.");let{static:a}=c.useContext(M);I(!a,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:u}=c.useContext(U),{pathname:s}=z(),f=Re(),l=ve(e,ge(u),s,o==="path"),n=JSON.stringify(l);return c.useEffect(()=>{f(JSON.parse(n),{replace:t,state:r,relative:o})},[f,n,o,t,r]),null}function Gt(e){L(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Jt({basename:e="/",children:t=null,location:r,navigationType:o="POP",navigator:a,static:u=!1}){L(!G(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=e.replace(/^\/*/,"/"),f=c.useMemo(()=>({basename:s,navigator:a,static:u,future:{}}),[s,a,u]);typeof r=="string"&&(r=V(r));let{pathname:l="/",search:n="",hash:d="",state:g=null,key:y="default"}=r,R=c.useMemo(()=>{let m=W(l,s);return m==null?null:{location:{pathname:m,search:n,hash:d,state:g,key:y},navigationType:o}},[s,l,n,d,g,y,o]);return I(R!=null,`<Router basename="${s}"> is not able to match the URL "${l}${n}${d}" because it does not start with the basename, so the <Router> won't render anything.`),R==null?null:c.createElement(M.Provider,{value:f},c.createElement(ee.Provider,{children:t,value:R}))}function Ar({children:e,location:t}){return It(me(e),t)}function me(e,t=[]){let r=[];return c.Children.forEach(e,(o,a)=>{if(!c.isValidElement(o))return;let u=[...t,a];if(o.type===c.Fragment){r.push.apply(r,me(o.props.children,u));return}L(o.type===Gt,`[${typeof o.type=="string"?o.type:o.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),L(!o.props.index||!o.props.children,"An index route cannot have child routes.");let s={id:o.props.id||u.join("-"),caseSensitive:o.props.caseSensitive,element:o.props.element,Component:o.props.Component,index:o.props.index,path:o.props.path,loader:o.props.loader,action:o.props.action,hydrateFallbackElement:o.props.hydrateFallbackElement,HydrateFallback:o.props.HydrateFallback,errorElement:o.props.errorElement,ErrorBoundary:o.props.ErrorBoundary,hasErrorBoundary:o.props.hasErrorBoundary===!0||o.props.ErrorBoundary!=null||o.props.errorElement!=null,shouldRevalidate:o.props.shouldRevalidate,handle:o.props.handle,lazy:o.props.lazy};o.props.children&&(s.children=me(o.props.children,u)),r.push(s)}),r}var ae="get",oe="application/x-www-form-urlencoded";function le(e){return e!=null&&typeof e.tagName=="string"}function Xt(e){return le(e)&&e.tagName.toLowerCase()==="button"}function Qt(e){return le(e)&&e.tagName.toLowerCase()==="form"}function Zt(e){return le(e)&&e.tagName.toLowerCase()==="input"}function er(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function tr(e,t){return e.button===0&&(!t||t==="_self")&&!er(e)}function ye(e=""){return new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let o=e[r];return t.concat(Array.isArray(o)?o.map(a=>[r,a]):[[r,o]])},[]))}function rr(e,t){let r=ye(e);return t&&t.forEach((o,a)=>{r.has(a)||t.getAll(a).forEach(u=>{r.append(a,u)})}),r}var ne=null;function nr(){if(ne===null)try{new FormData(document.createElement("form"),0),ne=!1}catch{ne=!0}return ne}var ar=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function he(e){return e!=null&&!ar.has(e)?(I(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${oe}"`),null):e}function or(e,t){let r,o,a,u,s;if(Qt(e)){let f=e.getAttribute("action");o=f?W(f,t):null,r=e.getAttribute("method")||ae,a=he(e.getAttribute("enctype"))||oe,u=new FormData(e)}else if(Xt(e)||Zt(e)&&(e.type==="submit"||e.type==="image")){let f=e.form;if(f==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||f.getAttribute("action");if(o=l?W(l,t):null,r=e.getAttribute("formmethod")||f.getAttribute("method")||ae,a=he(e.getAttribute("formenctype"))||he(f.getAttribute("enctype"))||oe,u=new FormData(f,e),!nr()){let{name:n,type:d,value:g}=e;if(d==="image"){let y=n?`${n}.`:"";u.append(`${y}x`,"0"),u.append(`${y}y`,"0")}else n&&u.append(n,g)}}else{if(le(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=ae,o=null,a=oe,s=e}return u&&a==="text/plain"&&(s=u,u=void 0),{action:o,method:r.toLowerCase(),encType:a,formData:u,body:s}}function Se(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function ir(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function ur(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function lr(e,t,r){let o=await Promise.all(e.map(async a=>{let u=t.routes[a.route.id];if(u){let s=await ir(u,r);return s.links?s.links():[]}return[]}));return dr(o.flat(1).filter(ur).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function Ie(e,t,r,o,a,u){let s=(l,n)=>r[n]?l.route.id!==r[n].route.id:!0,f=(l,n)=>r[n].pathname!==l.pathname||r[n].route.path?.endsWith("*")&&r[n].params["*"]!==l.params["*"];return u==="assets"?t.filter((l,n)=>s(l,n)||f(l,n)):u==="data"?t.filter((l,n)=>{let d=o.routes[l.route.id];if(!d||!d.hasLoader)return!1;if(s(l,n)||f(l,n))return!0;if(l.route.shouldRevalidate){let g=l.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:l.params,defaultShouldRevalidate:!0});if(typeof g=="boolean")return g}return!0}):[]}function sr(e,t,{includeHydrateFallback:r}={}){return cr(e.map(o=>{let a=t.routes[o.route.id];if(!a)return[];let u=[a.module];return a.clientActionModule&&(u=u.concat(a.clientActionModule)),a.clientLoaderModule&&(u=u.concat(a.clientLoaderModule)),r&&a.hydrateFallbackModule&&(u=u.concat(a.hydrateFallbackModule)),a.imports&&(u=u.concat(a.imports)),u}).flat(1))}function cr(e){return[...new Set(e)]}function fr(e){let t={},r=Object.keys(e).sort();for(let o of r)t[o]=e[o];return t}function dr(e,t){let r=new Set;return new Set(t),e.reduce((o,a)=>{let u=JSON.stringify(fr(a));return r.has(u)||(r.add(u),o.push({key:u,link:a})),o},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var hr=new Set([100,101,204,205]);function pr(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&W(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function Ve(){let e=c.useContext(K);return Se(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function mr(){let e=c.useContext(ue);return Se(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var xe=c.createContext(void 0);xe.displayName="FrameworkContext";function Ke(){let e=c.useContext(xe);return Se(e,"You must render this element inside a <HydratedRouter> element"),e}function yr(e,t){let r=c.useContext(xe),[o,a]=c.useState(!1),[u,s]=c.useState(!1),{onFocus:f,onBlur:l,onMouseEnter:n,onMouseLeave:d,onTouchStart:g}=t,y=c.useRef(null);c.useEffect(()=>{if(e==="render"&&s(!0),e==="viewport"){let v=h=>{h.forEach(_=>{s(_.isIntersecting)})},E=new IntersectionObserver(v,{threshold:.5});return y.current&&E.observe(y.current),()=>{E.disconnect()}}},[e]),c.useEffect(()=>{if(o){let v=setTimeout(()=>{s(!0)},100);return()=>{clearTimeout(v)}}},[o]);let R=()=>{a(!0)},m=()=>{a(!1),s(!1)};return r?e!=="intent"?[u,y,{}]:[u,y,{onFocus:Q(f,R),onBlur:Q(l,m),onMouseEnter:Q(n,R),onMouseLeave:Q(d,m),onTouchStart:Q(g,R)}]:[!1,y,{}]}function Q(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function gr({page:e,...t}){let{router:r}=Ve(),o=c.useMemo(()=>He(r.routes,e,r.basename),[r.routes,e,r.basename]);return o?c.createElement(Er,{page:e,matches:o,...t}):null}function vr(e){let{manifest:t,routeModules:r}=Ke(),[o,a]=c.useState([]);return c.useEffect(()=>{let u=!1;return lr(e,t,r).then(s=>{u||a(s)}),()=>{u=!0}},[e,t,r]),o}function Er({page:e,matches:t,...r}){let o=z(),{manifest:a,routeModules:u}=Ke(),{basename:s}=Ve(),{loaderData:f,matches:l}=mr(),n=c.useMemo(()=>Ie(e,t,l,a,o,"data"),[e,t,l,a,o]),d=c.useMemo(()=>Ie(e,t,l,a,o,"assets"),[e,t,l,a,o]),g=c.useMemo(()=>{if(e===o.pathname+o.search+o.hash)return[];let m=new Set,v=!1;if(t.forEach(h=>{let _=a.routes[h.route.id];!_||!_.hasLoader||(!n.some(b=>b.route.id===h.route.id)&&h.route.id in f&&u[h.route.id]?.shouldRevalidate||_.hasClientLoader?v=!0:m.add(h.route.id))}),m.size===0)return[];let E=pr(e,s);return v&&m.size>0&&E.searchParams.set("_routes",t.filter(h=>m.has(h.route.id)).map(h=>h.route.id).join(",")),[E.pathname+E.search]},[s,f,o,a,n,t,e,u]),y=c.useMemo(()=>sr(d,a),[d,a]),R=vr(d);return c.createElement(c.Fragment,null,g.map(m=>c.createElement("link",{key:m,rel:"prefetch",as:"fetch",href:m,...r})),y.map(m=>c.createElement("link",{key:m,rel:"modulepreload",href:m,...r})),R.map(({key:m,link:v})=>c.createElement("link",{key:m,...v})))}function Rr(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var Ge=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Ge&&(window.__reactRouterVersion="7.6.2")}catch{}function Nr({basename:e,children:t,window:r}){let o=c.useRef();o.current==null&&(o.current=ut({window:r,v5Compat:!0}));let a=o.current,[u,s]=c.useState({action:a.action,location:a.location}),f=c.useCallback(l=>{c.startTransition(()=>s(l))},[s]);return c.useLayoutEffect(()=>a.listen(f),[a,f]),c.createElement(Jt,{basename:e,children:t,location:u.location,navigationType:u.action,navigator:a})}var Je=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Xe=c.forwardRef(function({onClick:t,discover:r="render",prefetch:o="none",relative:a,reloadDocument:u,replace:s,state:f,target:l,to:n,preventScrollReset:d,viewTransition:g,...y},R){let{basename:m}=c.useContext(M),v=typeof n=="string"&&Je.test(n),E,h=!1;if(typeof n=="string"&&v&&(E=n,Ge))try{let A=new URL(window.location.href),H=n.startsWith("//")?new URL(A.protocol+n):new URL(n),J=W(H.pathname,m);H.origin===A.origin&&J!=null?n=J+H.search+H.hash:h=!0}catch{I(!1,`<Link to="${n}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let _=Nt(n,{relative:a}),[b,k,x]=yr(o,y),F=xr(n,{replace:s,state:f,target:l,preventScrollReset:d,relative:a,viewTransition:g});function D(A){t&&t(A),A.defaultPrevented||F(A)}let B=c.createElement("a",{...y,...x,href:E||_,onClick:h||u?t:D,ref:Rr(R,k),target:l,"data-discover":!v&&r==="render"?"true":void 0});return b&&!v?c.createElement(c.Fragment,null,B,c.createElement(gr,{page:_})):B});Xe.displayName="Link";var wr=c.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:o="",end:a=!1,style:u,to:s,viewTransition:f,children:l,...n},d){let g=te(s,{relative:n.relative}),y=z(),R=c.useContext(ue),{navigator:m,basename:v}=c.useContext(M),E=R!=null&&Lr(g)&&f===!0,h=m.encodeLocation?m.encodeLocation(g).pathname:g.pathname,_=y.pathname,b=R&&R.navigation&&R.navigation.location?R.navigation.location.pathname:null;r||(_=_.toLowerCase(),b=b?b.toLowerCase():null,h=h.toLowerCase()),b&&v&&(b=W(b,v)||b);const k=h!=="/"&&h.endsWith("/")?h.length-1:h.length;let x=_===h||!a&&_.startsWith(h)&&_.charAt(k)==="/",F=b!=null&&(b===h||!a&&b.startsWith(h)&&b.charAt(h.length)==="/"),D={isActive:x,isPending:F,isTransitioning:E},B=x?t:void 0,A;typeof o=="function"?A=o(D):A=[o,x?"active":null,F?"pending":null,E?"transitioning":null].filter(Boolean).join(" ");let H=typeof u=="function"?u(D):u;return c.createElement(Xe,{...n,"aria-current":B,className:A,ref:d,style:H,to:s,viewTransition:f},typeof l=="function"?l(D):l)});wr.displayName="NavLink";var Cr=c.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:o,replace:a,state:u,method:s=ae,action:f,onSubmit:l,relative:n,preventScrollReset:d,viewTransition:g,...y},R)=>{let m=br(),v=Tr(f,{relative:n}),E=s.toLowerCase()==="get"?"get":"post",h=typeof f=="string"&&Je.test(f),_=b=>{if(l&&l(b),b.defaultPrevented)return;b.preventDefault();let k=b.nativeEvent.submitter,x=k?.getAttribute("formmethod")||s;m(k||b.currentTarget,{fetcherKey:t,method:x,navigate:r,replace:a,state:u,relative:n,preventScrollReset:d,viewTransition:g})};return c.createElement("form",{ref:R,method:E,action:v,onSubmit:o?l:_,...y,"data-discover":!h&&e==="render"?"true":void 0})});Cr.displayName="Form";function Sr(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Qe(e){let t=c.useContext(K);return L(t,Sr(e)),t}function xr(e,{target:t,replace:r,state:o,preventScrollReset:a,relative:u,viewTransition:s}={}){let f=Re(),l=z(),n=te(e,{relative:u});return c.useCallback(d=>{if(tr(d,t)){d.preventDefault();let g=r!==void 0?r:Z(l)===Z(n);f(e,{replace:g,state:o,preventScrollReset:a,relative:u,viewTransition:s})}},[l,f,n,r,o,t,e,a,u,s])}function Dr(e){I(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let t=c.useRef(ye(e)),r=c.useRef(!1),o=z(),a=c.useMemo(()=>rr(o.search,r.current?null:t.current),[o.search]),u=Re(),s=c.useCallback((f,l)=>{const n=ye(typeof f=="function"?f(a):f);r.current=!0,u("?"+n,l)},[u,a]);return[a,s]}var _r=0,Pr=()=>`__${String(++_r)}__`;function br(){let{router:e}=Qe("useSubmit"),{basename:t}=c.useContext(M),r=Yt();return c.useCallback(async(o,a={})=>{let{action:u,method:s,encType:f,formData:l,body:n}=or(o,t);if(a.navigate===!1){let d=a.fetcherKey||Pr();await e.fetch(d,r,a.action||u,{preventScrollReset:a.preventScrollReset,formData:l,body:n,formMethod:a.method||s,formEncType:a.encType||f,flushSync:a.flushSync})}else await e.navigate(a.action||u,{preventScrollReset:a.preventScrollReset,formData:l,body:n,formMethod:a.method||s,formEncType:a.encType||f,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}function Tr(e,{relative:t}={}){let{basename:r}=c.useContext(M),o=c.useContext(U);L(o,"useFormAction must be used inside a RouteContext");let[a]=o.matches.slice(-1),u={...te(e||".",{relative:t})},s=z();if(e==null){u.search=s.search;let f=new URLSearchParams(u.search),l=f.getAll("index");if(l.some(d=>d==="")){f.delete("index"),l.filter(g=>g).forEach(g=>f.append("index",g));let d=f.toString();u.search=d?`?${d}`:""}}return(!e||e===".")&&a.route.index&&(u.search=u.search?u.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(u.pathname=u.pathname==="/"?r:j([r,u.pathname])),Z(u)}function Lr(e,t={}){let r=c.useContext(je);L(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=Qe("useViewTransitionState"),a=te(e,{relative:t.relative});if(!r.isTransitioning)return!1;let u=W(r.currentLocation.pathname,o)||r.currentLocation.pathname,s=W(r.nextLocation.pathname,o)||r.nextLocation.pathname;return ie(a.pathname,s)!=null||ie(a.pathname,u)!=null}[...hr];export{Nr as B,$r as N,Ar as R,kr as a,c as b,Or as c,Dr as d,Gt as e,nt as g,Me as r,Re as u};
