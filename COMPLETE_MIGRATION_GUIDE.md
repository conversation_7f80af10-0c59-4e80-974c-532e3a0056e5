# 🚀 Complete Migration Guide

## 📋 **File Organization Summary**

### **🔧 Server Repository (saffron-saga-server)**
```
saffron-saga-server/
├── src/                    # Copy from current server/src/
│   ├── controllers/
│   ├── models/
│   ├── routes/
│   ├── middlewares/
│   ├── config/
│   ├── db/
│   ├── scripts/
│   └── server.js
├── package.json           # Use SERVER_FILES/package.json
├── render.yaml            # Use SERVER_FILES/render.yaml
├── .env.example           # Use SERVER_FILES/.env.example
├── .nvmrc                 # Use SERVER_FILES/.nvmrc
├── .npmrc                 # Use SERVER_FILES/.npmrc
├── .gitignore             # Use SERVER_FILES/.gitignore
└── README.md              # Use SERVER_FILES/README.md
```

### **🌐 Client Repository (saffron-saga-client)**
```
saffron-saga-client/
├── src/                   # Copy from current client/src/
│   ├── components/
│   ├── pages/
│   ├── services/
│   ├── utils/
│   ├── config/
│   ├── assets/
│   ├── App.jsx
│   ├── main.jsx
│   └── index.css
├── public/                # Copy from current client/public/
├── package.json           # Use CLIENT_FILES/package.json
├── vercel.json            # Use CLIENT_FILES/vercel.json
├── vite.config.js         # Use CLIENT_FILES/vite.config.js
├── .env.example           # Use CLIENT_FILES/.env.example
├── .env.production        # Use CLIENT_FILES/.env.production
├── .gitignore             # Use CLIENT_FILES/.gitignore
├── README.md              # Use CLIENT_FILES/README.md
├── index.html             # Copy from current client/index.html
└── eslint.config.js       # Copy from current client/eslint.config.js
```

---

## 🔄 **Step-by-Step Migration**

### **Step 1: Create Server Repository**

1. **Create GitHub Repository:**
   - Name: `saffron-saga-server`
   - Description: "Saffron Saga Restaurant Backend API"
   - Private/Public: Your choice

2. **Setup Local Directory:**
   ```bash
   mkdir saffron-saga-server
   cd saffron-saga-server
   git init
   git remote add origin https://github.com/yourusername/saffron-saga-server.git
   ```

3. **Copy Server Files:**
   ```bash
   # Copy all server source code
   cp -r ../saffron-saga/server/src ./

   # Copy configuration files from SERVER_FILES/
   cp ../SERVER_FILES/package.json ./
   cp ../SERVER_FILES/render.yaml ./
   cp ../SERVER_FILES/.env.example ./
   cp ../SERVER_FILES/.nvmrc ./
   cp ../SERVER_FILES/.npmrc ./
   cp ../SERVER_FILES/.gitignore ./
   cp ../SERVER_FILES/README.md ./
   ```

4. **Update Environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

5. **Commit and Push:**
   ```bash
   git add .
   git commit -m "Initial server setup for split deployment"
   git push -u origin main
   ```

### **Step 2: Create Client Repository**

1. **Create GitHub Repository:**
   - Name: `saffron-saga-client`
   - Description: "Saffron Saga Restaurant Frontend"
   - Private/Public: Your choice

2. **Setup Local Directory:**
   ```bash
   mkdir saffron-saga-client
   cd saffron-saga-client
   git init
   git remote add origin https://github.com/yourusername/saffron-saga-client.git
   ```

3. **Copy Client Files:**
   ```bash
   # Copy all client source code
   cp -r ../saffron-saga/client/src ./
   cp -r ../saffron-saga/client/public ./
   cp ../saffron-saga/client/index.html ./
   cp ../saffron-saga/client/eslint.config.js ./

   # Copy configuration files from CLIENT_FILES/
   cp ../CLIENT_FILES/package.json ./
   cp ../CLIENT_FILES/vercel.json ./
   cp ../CLIENT_FILES/vite.config.js ./
   cp ../CLIENT_FILES/.env.example ./
   cp ../CLIENT_FILES/.env.production ./
   cp ../CLIENT_FILES/.gitignore ./
   cp ../CLIENT_FILES/README.md ./
   
   # Update API configuration
   cp ../CLIENT_FILES/src/config/api.js ./src/config/
   ```

4. **Update Environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your backend URL
   ```

5. **Commit and Push:**
   ```bash
   git add .
   git commit -m "Initial client setup for split deployment"
   git push -u origin main
   ```

---

## 🚀 **Step 3: Deploy Services**

### **🔧 Deploy Backend to Render**

1. **Go to Render Dashboard**
2. **New → Blueprint**
3. **Connect Repository:** `saffron-saga-server`
4. **Create Environment Group:** `saffron-saga-secrets`
   ```
   MONGODB_URI=mongodb+srv://ishukumar6949:<EMAIL>
   DB_NAME=saffronsaga
   BREVO_EMAIL=<EMAIL>
   BREVO_USER=<EMAIL>
   BREVO_PASS=y9g48v1VROJ3nQEr
   RESTAURANT_EMAIL=<EMAIL>
   ```
5. **Deploy Blueprint**

### **🌐 Deploy Frontend to Vercel**

1. **Go to Vercel Dashboard**
2. **New Project**
3. **Import Git Repository:** `saffron-saga-client`
4. **Framework Preset:** Vite
5. **Build Command:** `npm run build:prod`
6. **Output Directory:** `dist`
7. **Environment Variables:**
   ```
   VITE_API_BASE_URL=https://saffron-saga-server.onrender.com
   ```
8. **Deploy**

---

## 🔄 **Step 4: Update Cross-References**

### **After Backend Deployment:**
1. **Get Backend URL:** `https://saffron-saga-server.onrender.com`
2. **Update Frontend Environment:**
   - In Vercel dashboard, update `VITE_API_BASE_URL`
   - Redeploy frontend

### **After Frontend Deployment:**
1. **Get Frontend URL:** `https://saffron-saga-client.vercel.app`
2. **Update Backend CORS:**
   - In Render dashboard, update `CORS_ORIGIN`
   - Redeploy backend

---

## ✅ **Verification Checklist**

### **Backend Verification:**
- [ ] Health check: `https://saffron-saga-server.onrender.com/health`
- [ ] API endpoints responding
- [ ] Database connected
- [ ] Environment variables set

### **Frontend Verification:**
- [ ] Application loads: `https://saffron-saga-client.vercel.app`
- [ ] API calls working
- [ ] Menu items displaying
- [ ] Orders can be placed

### **Integration Verification:**
- [ ] No CORS errors
- [ ] End-to-end functionality working
- [ ] Admin panel accessible
- [ ] Email notifications working

---

## 🎯 **Expected Results**

✅ **Memory Issue Solved** - Each repository is much smaller
✅ **Faster Deployments** - Independent builds
✅ **Better Performance** - Platform-optimized deployments
✅ **Easier Maintenance** - Separated concerns

**🎉 Your application will now deploy successfully without memory constraints!**
