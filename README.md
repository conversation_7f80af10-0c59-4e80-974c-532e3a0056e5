# 🍛 Saffron Saga - Restaurant Ordering System

A modern full-stack restaurant application built with React, Node.js, Express, and MongoDB. Features online menu browsing, order placement, and admin management.

## ✨ Features

### Customer Features
- 📱 Responsive menu browsing
- 🛒 Shopping cart functionality
- 📝 Order placement with delivery details
- 📧 Email order confirmations

### Admin Features
- 🔐 Secure admin authentication
- 📋 Menu item management (CRUD operations)
- ⚡ Real-time availability updates
- 📊 Order notifications via email

### Technical Features
- 🚀 Production-ready deployment on Render
- 🔒 JWT-based authentication
- 🛡️ Rate limiting and security headers
- 📈 Health monitoring and logging
- 🌐 CORS-enabled API
- 📱 Mobile-responsive design

## 🏗️ Architecture

```
saffron-saga/
├── client/          # React frontend (Vite)
├── server/          # Node.js/Express API
├── docs/           # Documentation
├── render.yaml     # Render deployment config
└── PRODUCTION.md   # Production guide
```

## 🚀 Quick Start

### Production Deployment (Render)
1. Fork this repository
2. Follow the [Production Guide](PRODUCTION.md)
3. Deploy with one click using `render.yaml`

### Local Development

**Prerequisites:**
- Node.js >= 18.0.0
- MongoDB (local or Atlas)
- Git

**Setup:**
```bash
# Clone repository
git clone https://github.com/your-username/saffron-saga.git
cd saffron-saga

# Install dependencies
cd client && npm install
cd ../server && npm install

# Set up environment variables
cp server/.env.example server/.env
# Edit server/.env with your configuration

# Start development servers
npm run dev:server  # Backend on http://localhost:4000
npm run dev:client  # Frontend on http://localhost:5173
```

## 🔧 Environment Variables

### Server (.env)
```env
NODE_ENV=development
PORT=4000
MONGODB_URI=mongodb://localhost:27017/saffron_saga
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=http://localhost:5173

# Email configuration (Brevo)
BREVO_EMAIL=<EMAIL>
BREVO_USER=your-brevo-username
BREVO_PASS=your-brevo-password
RESTAURANT_EMAIL=<EMAIL>
```

### Client (.env)
```env
VITE_API_BASE_URL=http://localhost:4000
```

## 📚 API Documentation

Complete API documentation is available at [`docs/API_DOCUMENTATION.md`](docs/API_DOCUMENTATION.md)

### Key Endpoints
- `GET /health` - Health check
- `GET /api/menu` - Get menu items
- `POST /api/orders/create` - Create order
- `POST /api/auth/admin/login` - Admin login
- `POST /api/menu/create` - Create menu item (admin)

## 🛠️ Tech Stack

### Frontend
- **React 19** - UI framework
- **Vite** - Build tool and dev server
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **React Toastify** - Notifications
- **Yup** - Form validation

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM
- **JWT** - Authentication
- **Bcrypt** - Password hashing
- **Nodemailer** - Email service
- **Helmet** - Security headers
- **CORS** - Cross-origin requests

### DevOps & Deployment
- **Render** - Cloud platform
- **MongoDB Atlas** - Database hosting
- **Brevo** - Email service
- **GitHub** - Version control

## 📖 Documentation

- [📋 Production Deployment Guide](PRODUCTION.md)
- [🔌 Complete API Documentation](docs/API_DOCUMENTATION.md)
- [🚀 Detailed Render Deployment Guide](docs/RENDER_DEPLOYMENT_GUIDE.md)

## 🔐 Security Features

- JWT authentication with HTTP-only cookies
- Password hashing with bcrypt
- Rate limiting (100 requests per 15 minutes)
- CORS protection
- Input validation and sanitization
- Security headers with Helmet.js
- Environment variable protection

## 🎯 Production URLs

- **Frontend:** https://saffron-saga-client.onrender.com
- **Backend API:** https://saffron-saga-server.onrender.com
- **Health Check:** https://saffron-saga-server.onrender.com/health

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 📚 Documentation: [docs/](docs/)
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/saffron-saga/issues)

## 🙏 Acknowledgments

- Built with modern web technologies
- Deployed on Render cloud platform
- Email services powered by Brevo
- Database hosted on MongoDB Atlas

---

**Made with ❤️ for food lovers everywhere**
