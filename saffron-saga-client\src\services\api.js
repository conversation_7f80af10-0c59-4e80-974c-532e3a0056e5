// API Service Layer
import { apiClient, API_ENDPOINTS } from '../config/api.js';

/**
 * Health Check API Services
 */
export const healthService = {
  /**
   * Basic health check
   * @returns {Promise<Object>} Health check response
   */
  async checkHealth() {
    try {
      const response = await apiClient.get(API_ENDPOINTS.HEALTH);
      return {
        success: true,
        data: response.data,
        message: 'Server is healthy'
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Health check failed'
      };
    }
  },

  /**
   * Detailed health check with validation
   * @returns {Promise<Object>} Detailed health check response
   */
  async checkDetailedHealth() {
    try {
      const response = await apiClient.get(API_ENDPOINTS.HEALTH_DETAILED);
      return {
        success: true,
        data: response.data,
        message: 'Detailed health check completed'
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Detailed health check failed'
      };
    }
  }
};

/**
 * Menu API Services
 */
export const menuService = {
  /**
   * Fetch all menu items
   * @returns {Promise<Object>} Menu items response
   */
  async getMenuItems() {
    try {
      const response = await apiClient.get(API_ENDPOINTS.MENU_ITEMS);
      return {
        success: true,
        data: response.data.data || [],
        message: response.data.message
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error: error.response?.data?.message || error.message || 'Failed to fetch menu items'
      };
    }
  }
};

/**
 * Order API Services
 */
export const orderService = {
  /**
   * Create a new order
   * @param {Object} orderData - Order data containing cart and address
   * @returns {Promise<Object>} Order creation response
   */
  async createOrder(orderData) {
    try {
      const response = await apiClient.post(API_ENDPOINTS.CREATE_ORDER, orderData);
      return {
        success: true,
        data: response.data,
        orderId: response.data.orderId,
        message: response.data.message || 'Order placed successfully!'
      };
    } catch (error) {
      console.error('Order creation error:', error.response?.data);

      let errorMessage = 'Failed to create order';
      let validationErrors = null;

      if (error.response?.data) {
        const { message, errors } = error.response.data;
        errorMessage = message || errorMessage;

        // Handle validation errors
        if (errors && Array.isArray(errors)) {
          validationErrors = errors;
          const errorMessages = errors.map(err => err.msg || err.message).join(', ');
          errorMessage = `Validation failed: ${errorMessages}`;
        }
      }

      return {
        success: false,
        error: errorMessage,
        validationErrors,
        status: error.response?.status
      };
    }
  }
};

/**
 * Auth API Services
 */
export const authService = {
  /**
   * Register admin
   * @param {Object} adminData - Admin registration data
   * @returns {Promise<Object>} Registration response
   */
  async registerAdmin(adminData) {
    try {
      const response = await apiClient.post(API_ENDPOINTS.ADMIN_REGISTER, adminData);
      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Admin registered successfully!'
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to register admin',
        status: error.response?.status
      };
    }
  },

  /**
   * Login admin
   * @param {Object} credentials - Admin login credentials
   * @returns {Promise<Object>} Login response
   */
  async loginAdmin(credentials) {
    try {
      const response = await apiClient.post(API_ENDPOINTS.ADMIN_LOGIN, credentials);
      return {
        success: true,
        data: response.data,
        token: response.data.token,
        message: response.data.message || 'Login successful!'
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to login',
        status: error.response?.status
      };
    }
  },

  /**
   * Logout admin
   * @returns {Promise<Object>} Logout response
   */
  async logoutAdmin() {
    try {
      const response = await apiClient.post(API_ENDPOINTS.ADMIN_LOGOUT);
      return {
        success: true,
        message: response.data.message || 'Logout successful!'
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to logout',
        status: error.response?.status
      };
    }
  },

  /**
   * Check authentication status
   * @returns {Promise<Object>} Auth status response
   */
  async checkAuthStatus() {
    try {
      const response = await apiClient.get(API_ENDPOINTS.IS_AUTH);
      return {
        success: true,
        isAuthenticated: response.data.isAuthenticated || false,
        user: response.data.user
      };
    } catch (error) {
      return {
        success: false,
        isAuthenticated: false,
        error: error.response?.data?.message || error.message || 'Failed to check auth status'
      };
    }
  }
};

/**
 * Generic API utility functions
 */
export const apiUtils = {
  /**
   * Handle API response with consistent error handling
   * @param {Function} apiCall - The API call function
   * @param {Object} options - Options for error handling
   * @returns {Promise<Object>} Standardized response
   */
  async handleApiCall(apiCall, options = {}) {
    const { showToast = true, defaultErrorMessage = 'An error occurred' } = options;
    
    try {
      const result = await apiCall();
      
      if (showToast && result.success && result.message) {
        // You can import toast here if needed
        // toast.success(result.message);
      }
      
      return result;
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || defaultErrorMessage;
      
      if (showToast) {
        // You can import toast here if needed
        // toast.error(errorMessage);
      }
      
      return {
        success: false,
        error: errorMessage,
        status: error.response?.status
      };
    }
  },

  /**
   * Check if the API is reachable
   * @returns {Promise<boolean>} API reachability status
   */
  async checkApiHealth() {
    try {
      const response = await apiClient.get('/health', { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      console.error('API health check failed:', error);
      return false;
    }
  }
};
