#!/usr/bin/env node

/**
 * Test API endpoints directly
 */

import express from 'express';
import { MenuItem } from '../models/menuItem.model.js';
import connectToDB from '../db/db.js';

async function testMenuController() {
  try {
    console.log('🧪 Testing menu controller...');
    
    // Connect to database
    await connectToDB();
    console.log('✅ Database connected');
    
    // Test the controller logic directly
    const menuItems = await MenuItem.find({ isAvailable: true });
    console.log(`📊 Found ${menuItems.length} available menu items`);
    
    if (menuItems.length > 0) {
      console.log('📋 Sample items:');
      menuItems.slice(0, 3).forEach(item => {
        console.log(`  - ${item.name}: ₹${item.price} (${item.category})`);
      });
      
      // Test response format
      const response = {
        success: true,
        message: "Fetched all menu items successfully",
        data: menuItems,
      };
      
      console.log('✅ Response format is correct');
      console.log(`📦 Response size: ${JSON.stringify(response).length} bytes`);
    } else {
      console.log('⚠️  No menu items found');
    }
    
  } catch (error) {
    console.error('❌ Error testing controller:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    process.exit(0);
  }
}

testMenuController();
