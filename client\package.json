{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "set HOST=0.0.0.0 && react-scripts start", "dev": "vite --host", "build": "vite build", "build:prod": "vite build --mode production", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "preview:prod": "vite preview --mode production", "clean": "<PERSON><PERSON><PERSON> dist", "build:clean": "npm run clean && npm run build:prod"}, "dependencies": {"axios": "^1.9.0", "dotenv": "^16.5.0", "firebase": "^10.12.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.1", "react-toastify": "^11.0.5", "yup": "^1.6.1", "mongoose": "^8.0.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "vite-plugin-compression": "^0.5.1", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "rimraf": "^6.0.1", "terser": "^5.41.0", "vite": "^6.3.5"}}