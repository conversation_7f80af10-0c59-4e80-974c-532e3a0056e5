import React, { useMemo } from 'react';
import { assets } from '../assets/assets';
import { useImageLazyLoading } from '../hooks/useLazyLoading';
import { 
  getOptimizedImageUrl, 
  createResponsiveSrcSet, 
  getConnectionBasedOptimization,
  imagePerformanceTracker 
} from '../utils/imageOptimization';
import './LazyImage.css';

const OptimizedLazyImage = ({ 
  src, 
  alt, 
  className = '', 
  id = '', 
  fallbackSrc = assets.defaultImage,
  placeholder = null,
  onLoad = () => {},
  onError = () => {},
  rootMargin = '100px',
  threshold = 0.1,
  width,
  height,
  quality,
  format,
  responsive = false,
  responsiveSizes = [],
  trackPerformance = false,
  ...props 
}) => {
  // Get connection-based optimization settings
  const connectionOptimization = useMemo(() => getConnectionBasedOptimization(), []);
  
  // Optimize image URL based on provided options and connection
  const optimizedSrc = useMemo(() => {
    if (!src) return fallbackSrc;
    
    const optimizationOptions = {
      width,
      height,
      quality: quality || connectionOptimization.quality,
      format: format || connectionOptimization.format
    };
    
    return getOptimizedImageUrl(src, optimizationOptions);
  }, [src, width, height, quality, format, connectionOptimization, fallbackSrc]);

  // Create responsive srcSet if needed
  const srcSet = useMemo(() => {
    if (!responsive || !responsiveSizes.length || !src) return '';
    return createResponsiveSrcSet(src, responsiveSizes);
  }, [responsive, responsiveSizes, src]);

  const {
    elementRef,
    isLoaded,
    hasError,
    imageSrc,
    handleLoad: hookHandleLoad,
    handleError: hookHandleError,
    shouldLoad
  } = useImageLazyLoading(optimizedSrc, fallbackSrc, { rootMargin, threshold });

  // Performance tracking
  const performanceTracker = useMemo(() => {
    if (trackPerformance && src) {
      return imagePerformanceTracker.startTracking(src);
    }
    return null;
  }, [trackPerformance, src]);

  const handleLoad = () => {
    hookHandleLoad();
    if (performanceTracker) {
      performanceTracker.onLoad();
    }
    onLoad();
  };

  const handleError = () => {
    hookHandleError();
    if (performanceTracker) {
      performanceTracker.onError();
    }
    onError();
  };

  // Enhanced placeholder with skeleton loading
  const renderPlaceholder = () => {
    if (placeholder) return placeholder;
    
    return (
      <div className="lazy-image-loading-text">
        <div className="lazy-image-skeleton" style={{ width: '100%', height: '100%' }} />
      </div>
    );
  };

  return (
    <div 
      ref={elementRef}
      className={`lazy-image-container ${className} ${isLoaded ? 'loaded' : 'loading'}`}
      id={id}
      style={props.style}
    >
      {/* Enhanced placeholder with skeleton loading */}
      {!isLoaded && (
        <div className="lazy-image-placeholder">
          {renderPlaceholder()}
        </div>
      )}

      {/* Actual image with responsive support */}
      {shouldLoad && (
        <img
          src={imageSrc}
          srcSet={srcSet}
          sizes={responsive ? '(max-width: 768px) 100vw, 50vw' : undefined}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          className={`lazy-image ${isLoaded ? 'loaded' : 'loading'}`}
          loading="lazy" // Native lazy loading as fallback
          decoding="async" // Async decoding for better performance
          {...props}
        />
      )}

      {/* Error state */}
      {hasError && (
        <div className="lazy-image-error">
          <span>Failed to load image</span>
        </div>
      )}
    </div>
  );
};

export default OptimizedLazyImage;
