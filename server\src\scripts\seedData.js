#!/usr/bin/env node

/**
 * Seed Data Script
 * Populates database with sample data for development/testing
 */

import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import { MONGODB_URI, DB_NAME, SALT_ROUNDS, IS_PRODUCTION } from '../config/env.js';

// Import models
import { Admin } from '../models/admin.model.js';
import { MenuItem } from '../models/menuItem.model.js';

/**
 * Seed database with sample data
 */
async function seedDatabase() {
  try {
    // Don't seed in production unless explicitly requested
    if (IS_PRODUCTION && !process.argv.includes('--force')) {
      console.log('⚠️  Skipping seed data in production (use --force to override)');
      return;
    }

    // Always seed in development
    if (!IS_PRODUCTION) {
      console.log('🌱 Development mode - proceeding with seeding...');
    }
    
    console.log('🌱 Starting database seeding...');
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      dbName: DB_NAME,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
    });
    
    console.log('✅ Connected to MongoDB for seeding');
    
    // Seed data
    await seedAdminUsers();
    await seedMenuItems();
    
    console.log('🎉 Database seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Database seeding failed:', error.message);
    throw error;
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Seeding connection closed');
  }
}

/**
 * Seed admin users
 */
async function seedAdminUsers() {
  try {
    console.log('👤 Seeding admin users...');
    
    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      console.log('  ℹ️  Admin user already exists, skipping...');
      return;
    }
    
    // Create default admin
    const hashedPassword = await bcrypt.hash('Admin123!', SALT_ROUNDS);
    
    const admin = new Admin({
      first_name: 'saffron',
      last_name: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      isVerified: true
    });
    
    await admin.save();
    console.log('  ✅ Created default admin user');
    console.log('  📧 Email: <EMAIL>');
    console.log('  🔑 Password: Admin123!');
    
  } catch (error) {
    console.warn('  ⚠️  Admin seeding warning:', error.message);
  }
}

/**
 * Seed menu items
 */
async function seedMenuItems() {
  try {
    console.log('🍛 Seeding menu items...');
    
    // Check if menu items already exist
    const existingItems = await MenuItem.countDocuments();
    if (existingItems > 0) {
      console.log(`  ℹ️  Found ${existingItems} existing menu items, skipping seeding...`);
      return;
    }
    
    const sampleMenuItems = [
      {
        name: 'butter chicken',
        description: 'creamy tomato-based curry with tender chicken pieces served with basmati rice',
        category: 'main course',
        price: 350,
        quantity: 1,
        unit: 'plate',
        currency: 'INR',
        isAvailable: true
      },
      {
        name: 'paneer tikka masala',
        description: 'grilled cottage cheese cubes in rich tomato and cream gravy',
        category: 'main course',
        price: 320,
        quantity: 1,
        unit: 'plate',
        currency: 'INR',
        isAvailable: true
      },
      {
        name: 'biryani chicken',
        description: 'aromatic basmati rice cooked with spiced chicken and saffron',
        category: 'rice',
        price: 380,
        quantity: 1,
        unit: 'plate',
        currency: 'INR',
        isAvailable: true
      },
      {
        name: 'dal tadka',
        description: 'yellow lentils tempered with cumin, garlic and spices',
        category: 'dal',
        price: 180,
        quantity: 1,
        unit: 'bowl',
        currency: 'INR',
        isAvailable: true
      },
      {
        name: 'naan bread',
        description: 'soft and fluffy indian bread baked in tandoor oven',
        category: 'bread',
        price: 60,
        quantity: 1,
        unit: 'piece',
        currency: 'INR',
        isAvailable: true
      },
      {
        name: 'gulab jamun',
        description: 'soft milk dumplings soaked in rose-flavored sugar syrup',
        category: 'dessert',
        price: 120,
        quantity: 2,
        unit: 'pieces',
        currency: 'INR',
        isAvailable: true
      },
      {
        name: 'mango lassi',
        description: 'refreshing yogurt drink blended with sweet mango pulp',
        category: 'beverage',
        price: 80,
        quantity: 1,
        unit: 'glass',
        currency: 'INR',
        isAvailable: true
      },
      {
        name: 'samosa',
        description: 'crispy pastry filled with spiced potatoes and peas',
        category: 'appetizer',
        price: 40,
        quantity: 1,
        unit: 'piece',
        currency: 'INR',
        isAvailable: true
      }
    ];
    
    const createdItems = await MenuItem.insertMany(sampleMenuItems);
    console.log(`  ✅ Created ${createdItems.length} sample menu items`);
    
  } catch (error) {
    console.warn('  ⚠️  Menu items seeding warning:', error.message);
  }
}

/**
 * Handle script execution
 */
async function main() {
  try {
    await seedDatabase();
    process.exit(0);
  } catch (error) {
    console.error('💥 Seeding script failed:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { seedDatabase };
