# Production Environment Variables
# Note: In Render, these should be set through the dashboard, not this file

# Server Configuration
NODE_ENV=production
PORT=8080

# Database Configuration (Set in Render dashboard)
# MONGODB_URI=mongodb+srv://username:<EMAIL>
# DB_NAME=saffronsaga

# CORS Configuration (Set in Render dashboard)
# CORS_ORIGIN=https://saffron-saga-client-p.vercel.app

# Security Configuration (Set in Render dashboard)
# JWT_SECRET=your-production-jwt-secret
# SESSION_SECRET=your-production-session-secret
SALT_ROUNDS=12
TRUST_PROXY=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Service Configuration (Set in Render dashboard)
# RESTAURANT_EMAIL=<EMAIL>
# BREVO_EMAIL=<EMAIL>
# BREVO_USER=<EMAIL>
# BREVO_PASS=your-brevo-password
