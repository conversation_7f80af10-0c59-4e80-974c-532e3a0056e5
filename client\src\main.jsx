import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.jsx";
import { logEnvironmentStatus } from "./utils/env.js";
import { testProductionConnection } from "./utils/connectionTest.js";

// Validate environment variables and test connections in development
if (import.meta.env.VITE_NODE_ENV === 'development') {
  logEnvironmentStatus();

  // Run connection tests after a short delay
  setTimeout(() => {
    testProductionConnection();
  }, 2000);
}

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <App />
  </StrictMode>
);
