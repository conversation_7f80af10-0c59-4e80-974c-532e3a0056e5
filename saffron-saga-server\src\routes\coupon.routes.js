import { Router } from "express";
import {
  create<PERSON>oupon<PERSON><PERSON>roller,
  getAllCoupons<PERSON><PERSON>roller,
  validateCouponController,
  updateCouponStatusController,
  deleteCouponController
} from "../controllers/coupon.controller.js";
import {
  createCouponValidation,
  validateCouponValidation,
  updateCouponStatusValidation
} from "../validators/coupon.validator.js";
import { authorizeAdmin } from "../middlewares/auth.middleware.js";

const router = Router();

// Public routes
// Validate and apply coupon
router.post("/validate", validateCouponValidation, validateCouponController);

// Admin routes (require authentication)
// Create new coupon
router.post("/create", authorizeAdmin, createCouponValidation, createCouponController);

// Get all coupons with pagination and filtering
router.get("/", authorizeAdmin, getAllCouponsController);

// Update coupon status (activate/deactivate)
router.patch("/:id/status", authorizeAdmin, updateCouponStatusValidation, updateCouponStatusController);

// Delete coupon
router.delete("/:id", authorizeAdmin, deleteCouponController);

export default router;
