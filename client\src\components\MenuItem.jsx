import React, { useContext } from "react";
import { MenuContext } from "../context/MenuContext";
import "react-toastify/dist/ReactToastify.css";
import "./menuitem.css";
import { CartContext } from "../context/CartContext";
import { assets } from "../assets/assets";

const MenuItem = () => {
  const { menuItems } = useContext(MenuContext);
  const { cart, setCart } = useContext(CartContext);

  const addToCart = (id) => {
    setCart((prevCart) => {
      const existingItem = prevCart.find((item) => item._id === id);
      if (existingItem) {
        return prevCart.map((item) =>
          item._id === id ? { ...item, quantity: item.quantity + 1 } : item
        );
      } else {
        // Find the menu item to get complete details
        const menuItem = menuItems.find((item) => item._id === id);
        if (!menuItem) {
          console.error('Menu item not found for ID:', id);
          return prevCart;
        }

        return [...prevCart, {
          _id: id,
          quantity: 1,
          name: menuItem.name,
          price: menuItem.price,
          unit: menuItem.unit,
          description: menuItem.description
        }];
      }
    });
  };

  const increment = (id) => {
    setCart((prevCart) =>
      prevCart.map((item) =>
        item._id === id ? { ...item, quantity: item.quantity + 1 } : item
      )
    );
  };

  const decrement = (id) => {
    setCart((prevCart) => {
      const item = prevCart.find((item) => item._id === id);
      if (item.quantity === 1) {
        return prevCart.filter((item) => item._id !== id);
      }
      return prevCart.map((item) =>
        item._id === id ? { ...item, quantity: item.quantity - 1 } : item
      );
    });
  };

  return (
    <>
      {menuItems.length === 0 ? (
        <p>No items available right now.</p>
      ) : (
        menuItems.map((item) => {
          const cartItem = cart.find((cartItem) => cartItem._id === item._id);

          return (
            <div key={item._id} id="item-container">
              <div className="item-left">
                <img src={item.image || assets.defaultImage} alt={item.name} />
              </div>
              <div className="item-right">
                <div className="right-top">
                  <h2>{item.name}</h2>
                </div>
                <div className="right-middle">
                  <p>{item.description}</p>
                </div>
                <div className="right-bottom">
                  <div className="right-bottom-left">
                    <h4>
                      {new Intl.NumberFormat("en-IN", {
                        style: "currency",
                        currency: "INR",
                      }).format(item.price)}
                    </h4>
                    <h6>
                      {item.quantity} {item.unit}
                    </h6>
                  </div>
                  <div className="right-bottom-right">
                    {cartItem ? (
                      <div className="counter">
                        <button onClick={() => decrement(item._id)}>-</button>
                        <span>{cartItem.quantity}</span>
                        <button onClick={() => increment(item._id)}>+</button>
                      </div>
                    ) : (
                      <button
                        className="btn"
                        onClick={() => addToCart(item._id)}
                      >
                        Add
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })
      )}
    </>
  );
};

export default MenuItem;
