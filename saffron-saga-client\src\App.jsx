import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { MenuProvider } from "./context/MenuContext";
import "./app.css";
import Home from "./pages/Home";
import { CartProvider } from "./context/CartContext";
import Cart from "./pages/Cart";
import Order from "./pages/Order";
import ProtectedOrderRoute from "./components/ProtectedOrderRoute";
import Confirm from "./pages/Confirm";

const App = () => {
  return (
    <MenuProvider>
      <CartProvider>
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/cart" element={<Cart />} />
            <Route
              path="/order"
              element={
                <ProtectedOrderRoute>
                  <Order />
                </ProtectedOrderRoute>
              }
            />
            <Route path="/order/confirm" element={<Confirm />} />
          </Routes>
        </BrowserRouter>
      </CartProvider>
    </MenuProvider>
  );
};

export default App;
