*,
*::before,
*::after {
  box-sizing: border-box;
}
* {
  margin: 0;
  padding: 0;
}
html,
body {
  height: 100%;
  width: 100%;
}
:root {
  --font-c: #000000;
  --bg-c: #ffffff;
  --btn-bg: #ffdd00;
}

#header {
  width: 100%;
  height: 52px;
  background-color: var(--bg-c);
  color: var(--font-c);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-inline: 0.5rem;
}
#navlogo {
  width: 128px;
}
#menu-icon {
  font-size: 2.4rem;
  font-weight: 300;
}
 
 