// Environment validation utility

/**
 * Validates that all required environment variables are present
 * @returns {Object} Validation result with isValid boolean and missing variables array
 */
export const validateEnvironment = () => {
  const requiredVars = ["VITE_API_BASE_URL", "VITE_NODE_ENV"];
  const productionVars = [
    "VITE_FIREBASE_API_KEY",
    "VITE_FIREBASE_AUTH_DOMAIN",
    "VITE_FIREBASE_PROJECT_ID",
    "VITE_FIREBASE_STORAGE_BUCKET",
    "VITE_FIREBASE_MESSAGING_SENDER_ID",
    "VITE_FIREBASE_APP_ID"
  ];

  const missing = requiredVars.filter((varName) => !import.meta.env[varName]);

  // Check production-specific variables
  if (import.meta.env.VITE_NODE_ENV === 'production') {
    const missingProd = productionVars.filter((varName) => !import.meta.env[varName]);
    missing.push(...missingProd);
  }

  // Validate API URL format
  const apiUrl = import.meta.env.VITE_API_BASE_URL;
  const urlValidation = validateApiUrl(apiUrl);

  return {
    isValid: missing.length === 0 && urlValidation.isValid,
    missing,
    urlError: urlValidation.error,
    environment: import.meta.env.VITE_NODE_ENV || "unknown",
  };
};

/**
 * Validates API URL format
 * @param {string} url - API URL to validate
 * @returns {Object} Validation result
 */
export const validateApiUrl = (url) => {
  if (!url) {
    return { isValid: false, error: 'API URL is required' };
  }

  try {
    const urlObj = new URL(url);

    // Check protocol
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return { isValid: false, error: 'API URL must use HTTP or HTTPS protocol' };
    }

    // In production, enforce HTTPS
    if (import.meta.env.VITE_NODE_ENV === 'production' && urlObj.protocol !== 'https:') {
      return { isValid: false, error: 'Production API URL must use HTTPS' };
    }

    // Check for localhost in production
    if (import.meta.env.VITE_NODE_ENV === 'production' &&
        (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1')) {
      return { isValid: false, error: 'Production API URL cannot use localhost' };
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Invalid API URL format' };
  }
};

/**
 * Logs environment validation results to console
 */
export const logEnvironmentStatus = () => {
  const validation = validateEnvironment();

  if (validation.isValid) {
    console.log(`✅ Environment validation passed (${validation.environment})`);
    console.log(`🔗 API Base URL: ${import.meta.env.VITE_API_BASE_URL}`);
  } else {
    console.error("❌ Environment validation failed!");
    if (validation.missing.length > 0) {
      console.error("Missing variables:", validation.missing);
    }
    if (validation.urlError) {
      console.error("URL validation error:", validation.urlError);
    }
  }

  return validation;
};

/**
 * Gets current environment info
 */
export const getEnvironmentInfo = () => ({
  mode: import.meta.env.MODE,
  nodeEnv: import.meta.env.VITE_NODE_ENV,
  isDevelopment: import.meta.env.VITE_NODE_ENV === "development",
  isProduction: import.meta.env.VITE_NODE_ENV === "production",
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
  buildTime: import.meta.env.VITE_BUILD_TIME || new Date().toISOString(),
});

/**
 * Check if API is reachable
 * @returns {Promise<boolean>} API reachability status
 */
export const checkApiConnection = async () => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/health`, {
      method: 'GET',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.error('API connection check failed:', error);
    return false;
  }
};
