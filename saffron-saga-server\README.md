# 🍛 Saffron Saga Server

Backend API for the Saffron Saga Restaurant Application.

## 🚀 Quick Start

### Prerequisites
- Node.js 20.11.0 or higher
- MongoDB Atlas account
- Brevo email service account

### Local Development
```bash
# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Update .env with your credentials

# Start development server
npm run dev
```

### Production Deployment (Render)

1. **Create new repository** for server-only code
2. **Push this server code** to the new repository
3. **Deploy to Render** using the included render.yaml

#### Environment Variables Required:
- `MONGODB_URI` - MongoDB connection string
- `DB_NAME` - Database name
- `BREVO_EMAIL` - Brevo email address
- `BREVO_USER` - Brevo SMTP username
- `BREVO_PASS` - Brevo SMTP password
- `RESTAURANT_EMAIL` - Restaurant notification email

## 📊 API Endpoints

### Health Check
- `GET /health` - Server health status

### Menu Management
- `GET /api/menu` - Get all available menu items
- `POST /api/menu/create` - Create new menu item (Admin)
- `POST /api/menu/edit-avilability` - Update item availability (Admin)

### Order Management
- `POST /api/orders/create` - Create new order

### Authentication
- `POST /api/auth/admin/register` - Register admin
- `POST /api/auth/admin/login` - Admin login
- `POST /api/auth/admin/logout` - Admin logout

## 🔧 Scripts

- `npm run dev` - Start development server
- `npm start` - Start production server
- `npm run db:setup` - Setup database indexes
- `npm run db:seed` - Seed sample data
- `npm run db:migrate` - Run database migrations

## 🌐 CORS Configuration

Update `CORS_ORIGIN` environment variable with your frontend URL:
- Development: `http://localhost:3000`
- Production: `https://your-frontend.vercel.app`

## 📝 License

ISC License
