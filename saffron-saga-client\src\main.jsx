import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.jsx";
import { logEnvironmentStatus } from "./utils/env.js";

// Validate environment variables in development
if (import.meta.env.VITE_NODE_ENV === 'development') {
  logEnvironmentStatus();
}

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <App />
  </StrictMode>
);
