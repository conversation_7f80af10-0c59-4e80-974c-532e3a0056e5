PORT=8080
MONGODB_URI="mongodb+srv://ishukumar6949:<EMAIL>"
DB_NAME="saffronsaga"
NODE_ENV="production"
CORS_ORIGIN="https://saffron-saga-client.onrender.com"
# JWT_SECRET=auto-generated-by-render
SALT_ROUNDS=12

RESTAURANT_EMAIL=<EMAIL>

BREVO_EMAIL=<EMAIL>
BREVO_USER=<EMAIL>
BREVO_PASS=y9g48v1VROJ3nQEr

# Production specific settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
# SESSION_SECRET=auto-generated-by-render
TRUST_PROXY=true
