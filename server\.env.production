PORT=8080
MONGODB_URI="mongodb+srv://ishukumar6949:<EMAIL>"
DB_NAME="saffronsaga"
NODE_ENV="production"
CORS_ORIGIN="https://saffron-saga-client.vercel.app,https://*.vercel.app,https://*.netlify.app,https://*.herokuapp.com,https://*.railway.app,https://*.render.com,http://localhost:5173,http://*************:5173"
JWT_SECRET=your-super-secure-jwt-secret-for-production
SALT_ROUNDS=12

RESTAURANT_EMAIL=<EMAIL>

BREVO_EMAIL=<EMAIL>
BREVO_USER=<EMAIL>
BREVO_PASS=y9g48v1VROJ3nQEr

# Production specific settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SESSION_SECRET=your-super-secure-session-secret
TRUST_PROXY=true
