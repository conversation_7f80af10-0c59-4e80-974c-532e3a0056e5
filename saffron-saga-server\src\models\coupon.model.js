import mongoose from "mongoose";

// Coupon Schema
const couponSchema = mongoose.Schema(
  {
    code: {
      type: String,
      required: true,
      unique: true,
      uppercase: true,
      trim: true,
      minlength: 3,
      maxlength: 20,
      match: [/^[A-Z0-9]+$/, 'Coupon code can only contain uppercase letters and numbers']
    },
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200
    },
    type: {
      type: String,
      enum: ['percentage', 'fixed'],
      required: true
    },
    value: {
      type: Number,
      required: true,
      min: 0
    },
    minimumOrderAmount: {
      type: Number,
      default: 0,
      min: 0
    },
    maximumDiscountAmount: {
      type: Number,
      default: null, // null means no maximum limit
      min: 0
    },
    startDate: {
      type: Date,
      required: true,
      default: Date.now
    },
    endDate: {
      type: Date,
      required: true,
      validate: {
        validator: function(value) {
          return value > this.startDate;
        },
        message: 'End date must be after start date'
      }
    },
    usageLimit: {
      type: Number,
      default: null, // null means unlimited usage
      min: 1
    },
    usedCount: {
      type: Number,
      default: 0,
      min: 0
    },
    perUserLimit: {
      type: Number,
      default: 1, // Default: each user can use once
      min: 1
    },
    isActive: {
      type: Boolean,
      default: true
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Admin",
      required: true
    },
    usedBy: [{
      userId: {
        type: String, // For guest users, we'll use session/IP
        required: true
      },
      usedAt: {
        type: Date,
        default: Date.now
      },
      orderAmount: {
        type: Number,
        required: true
      },
      discountApplied: {
        type: Number,
        required: true
      }
    }]
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual for checking if coupon is expired
couponSchema.virtual('isExpired').get(function() {
  return new Date() > this.endDate;
});

// Virtual for checking if coupon is valid (active and not expired)
couponSchema.virtual('isValid').get(function() {
  return this.isActive && !this.isExpired;
});

// Virtual for remaining usage count
couponSchema.virtual('remainingUsage').get(function() {
  if (this.usageLimit === null) return null; // Unlimited
  return Math.max(0, this.usageLimit - this.usedCount);
});

// Instance method to check if user can use this coupon
couponSchema.methods.canUserUse = function(userId) {
  const userUsageCount = this.usedBy.filter(usage => usage.userId === userId).length;
  return userUsageCount < this.perUserLimit;
};

// Instance method to calculate discount for given amount
couponSchema.methods.calculateDiscount = function(orderAmount) {
  if (!this.isValid) return 0;
  if (orderAmount < this.minimumOrderAmount) return 0;
  
  let discount = 0;
  
  if (this.type === 'percentage') {
    discount = (orderAmount * this.value) / 100;
  } else if (this.type === 'fixed') {
    discount = this.value;
  }
  
  // Apply maximum discount limit if set
  if (this.maximumDiscountAmount && discount > this.maximumDiscountAmount) {
    discount = this.maximumDiscountAmount;
  }
  
  // Discount cannot exceed order amount
  return Math.min(discount, orderAmount);
};

// Static method to find valid coupon by code
couponSchema.statics.findValidCoupon = function(code) {
  return this.findOne({
    code: code.toUpperCase(),
    isActive: true,
    startDate: { $lte: new Date() },
    endDate: { $gte: new Date() }
  });
};

// Pre-save middleware to ensure code is uppercase
couponSchema.pre('save', function(next) {
  if (this.code) {
    this.code = this.code.toUpperCase();
  }
  next();
});

export const Coupon = mongoose.model("Coupon", couponSchema);
