import mongoose from "mongoose";
import { DB_NAME, MONGODB_URI, IS_PRODUCTION } from "../config/env.js";

const connectToDB = async () => {
  try {
    const connectionOptions = {
      // Connection stability options
      serverSelectionTimeoutMS: 5000, // Give up initial connection after 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
      family: 4, // Use IPv4, skip trying IPv6
      maxPoolSize: 10, // Maintain up to 10 socket connections
      minPoolSize: 5, // Maintain a minimum of 5 socket connections
      maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
    };

    const maxRetries = 3;
    let retries = 0;
    let lastError = null;

    while (retries < maxRetries) {
      try {
        const connectionInstance = await mongoose.connect(
          `${MONGODB_URI}/${DB_NAME}`,
          connectionOptions
        );

        if (!IS_PRODUCTION) {
          console.log(`MongoDB Connected! DB HOST: ${connectionInstance.connection.host}`);
        }

        // Set up connection error handler
        mongoose.connection.on("error", (error) => {
          console.error("MongoDB connection error:", error);
        });

        // Handle disconnection
        mongoose.connection.on("disconnected", () => {
          if (!IS_PRODUCTION) {
            console.warn("MongoDB disconnected. Attempting to reconnect...");
          }
        });

        // Handle successful reconnection
        mongoose.connection.on("reconnected", () => {
          if (!IS_PRODUCTION) {
            console.log("MongoDB reconnected successfully!");
          }
        });

        return;
      } catch (error) {
        lastError = error;
        retries++;
        if (retries < maxRetries) {
          if (!IS_PRODUCTION) {
            console.log(`MongoDB connection attempt ${retries} failed. Retrying...`);
          }
          await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait 2 seconds before retrying
        }
      }
    }

    throw new Error(
      `Failed to connect to MongoDB after ${maxRetries} attempts. Last error: ${lastError.message}`
    );
  } catch (error) {
    console.error(`❌ MongoDB connection error: ${error.message}`);
    process.exit(1);
  }
};

export default connectToDB;
