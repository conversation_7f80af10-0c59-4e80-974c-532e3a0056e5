# Saffron Saga Deployment Guide

This guide explains how to deploy the Saffron Saga application on Render.

## Prerequisites

1. A Render account
2. MongoDB Atlas database set up
3. Firebase project configured
4. Environment variables ready

## Deployment Steps

### Backend Deployment

1. Push your code to GitHub
2. In Render dashboard:
   - Create new Web Service
   - Connect your GitHub repository
   - Select the `main` branch
   - Set build command: `cd server && npm install && npm run build`
   - Set start command: `cd server && npm start`
   - Add environment variables from `.env.example`

### Frontend Deployment

1. In Render dashboard:
   - Create new Static Site
   - Connect your GitHub repository
   - Set build command: `cd client && npm install && npm run build`
   - Set publish directory: `client/dist`
   - Add environment variables from `.env.example`

## Environment Variables

Make sure to set all environment variables in Render dashboard as specified in:
- `server/.env.example` for backend
- `client/.env.example` for frontend

## Post-Deployment

1. Update CORS_ORIGIN in backend env vars with frontend URL
2. Update VITE_API_URL in frontend env vars with backend URL
3. Test the deployment by visiting the frontend URL

## Monitoring

Monitor your application using <PERSON><PERSON>'s built-in logging and metrics.

## Troubleshooting

1. Check Render logs for any deployment errors
2. Verify environment variables are set correctly
3. Ensure MongoDB connection is working
4. Check CORS settings if API calls fail
