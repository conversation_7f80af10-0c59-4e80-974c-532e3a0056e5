# Saffron Saga Server Environment Variables

# Server Configuration
NODE_ENV=production
PORT=8080

# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>
DB_NAME=saffronsaga

# CORS Configuration (Update with your frontend URL)
CORS_ORIGIN=https://your-frontend-domain.vercel.app

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret
SESSION_SECRET=your-super-secure-session-secret
SALT_ROUNDS=12
TRUST_PROXY=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Service Configuration (Brevo)
RESTAURANT_EMAIL=<EMAIL>
BREVO_EMAIL=<EMAIL>
BREVO_USER=your-brevo-smtp-user
BREVO_PASS=your-brevo-smtp-password
