import { body } from "express-validator";

// Validation rules for creating a coupon
export const createCouponValidation = [
  body("code")
    .notEmpty()
    .withMessage("Coupon code is required")
    .isLength({ min: 3, max: 20 })
    .withMessage("Coupon code must be between 3 and 20 characters")
    .matches(/^[A-Z0-9]+$/)
    .withMessage("Coupon code can only contain uppercase letters and numbers")
    .trim(),

  body("description")
    .notEmpty()
    .withMessage("Description is required")
    .isLength({ max: 200 })
    .withMessage("Description cannot exceed 200 characters")
    .trim(),

  body("type")
    .notEmpty()
    .withMessage("Coupon type is required")
    .isIn(['percentage', 'fixed'])
    .withMessage("Coupon type must be either 'percentage' or 'fixed'"),

  body("value")
    .notEmpty()
    .withMessage("Coupon value is required")
    .isFloat({ min: 0 })
    .withMessage("Coupon value must be a positive number")
    .custom((value, { req }) => {
      if (req.body.type === 'percentage' && value > 100) {
        throw new Error('Percentage discount cannot exceed 100%');
      }
      return true;
    }),

  body("minimumOrderAmount")
    .optional()
    .isFloat({ min: 0 })
    .withMessage("Minimum order amount must be a positive number"),

  body("maximumDiscountAmount")
    .optional()
    .isFloat({ min: 0 })
    .withMessage("Maximum discount amount must be a positive number"),

  body("startDate")
    .optional()
    .isISO8601()
    .withMessage("Start date must be a valid date")
    .toDate(),

  body("endDate")
    .notEmpty()
    .withMessage("End date is required")
    .isISO8601()
    .withMessage("End date must be a valid date")
    .toDate()
    .custom((value, { req }) => {
      const startDate = req.body.startDate ? new Date(req.body.startDate) : new Date();
      if (new Date(value) <= startDate) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),

  body("usageLimit")
    .optional()
    .isInt({ min: 1 })
    .withMessage("Usage limit must be a positive integer"),

  body("perUserLimit")
    .optional()
    .isInt({ min: 1 })
    .withMessage("Per user limit must be a positive integer")
];

// Validation rules for validating a coupon
export const validateCouponValidation = [
  body("code")
    .notEmpty()
    .withMessage("Coupon code is required")
    .trim(),

  body("orderAmount")
    .notEmpty()
    .withMessage("Order amount is required")
    .isFloat({ min: 0 })
    .withMessage("Order amount must be a positive number"),

  body("userId")
    .optional()
    .isString()
    .withMessage("User ID must be a string")
    .trim()
];

// Validation rules for updating coupon status
export const updateCouponStatusValidation = [
  body("isActive")
    .notEmpty()
    .withMessage("Active status is required")
    .isBoolean()
    .withMessage("Active status must be a boolean value")
];
