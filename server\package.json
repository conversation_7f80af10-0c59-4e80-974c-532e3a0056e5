{"name": "server", "version": "1.0.0", "description": "", "type": "module", "main": "server.js", "scripts": {"dev": "nodemon src/server.js", "start": "NODE_ENV=production node src/server.js", "start:prod": "npx cross-env NODE_ENV=production node src/server.js", "build": "echo 'No build step required for Node.js'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'", "clean": "echo 'No clean step required'", "db:setup": "node src/scripts/dbSetup.js", "db:seed": "node src/scripts/seedData.js", "db:migrate": "node src/scripts/migrate.js", "predeploy": "npm run db:setup", "deploy:prod": "npm run db:setup && npm run start:prod"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^6.0.0", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.1", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0", "nodemailer": "^7.0.3"}, "devDependencies": {"cross-env": "^7.0.3", "nodemon": "^3.1.7", "prettier": "^3.5.3"}}