import nodemailer from "nodemailer";
import { BREVO_EMAIL, BREVO_PASS, BREVO_USER } from "../config/env.js";

const transporter = nodemailer.createTransport({
  host: "smtp-relay.brevo.com",
  port: 587, // or 465 for SSL
  auth: {
    user: BREVO_USER, // from env
    pass: BREVO_PASS, // from env
  },
});

export const sendEmail = async ({ to, subject, text, html }) => {
  try {
    const info = await transporter.sendMail({
      from: `"Saffron Saga" <${BREVO_EMAIL}>`,
      to,
      subject,
      text,
      html,
    });
    console.log("Email sent:", info.response);
    return info;
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
};
