{"name": "saffron-saga", "version": "1.0.0", "description": "Fullstack Saffron Saga Restaurant Application", "private": true, "scripts": {"start": "cd server && npm start", "build": "cd server && npm run build", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start:prod": "cd server && npm run start:prod", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "db:setup": "cd server && npm run db:setup"}, "engines": {"node": ">=20.11.0", "npm": ">=9.0.0"}, "keywords": ["restaurant", "food-ordering", "fullstack", "mern", "react", "nodejs", "mongodb"], "author": "Saffron Saga Team", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/your-username/saffron-saga.git"}}