/* Lazy Image Component Styles */
.lazy-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  display: block;
}

.lazy-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #999;
  font-size: 14px;
  z-index: 1;
}

.lazy-image-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: lazy-image-spin 1s linear infinite;
  margin: 0 auto 10px;
}

.lazy-image-loading-text {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease-in-out;
  display: block;
}

.lazy-image.loaded {
  opacity: 1;
}

.lazy-image.loading {
  opacity: 0;
}

/* Loading spinner animation */
@keyframes lazy-image-spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}

/* Fade in animation for loaded images */
@keyframes lazy-image-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.lazy-image-fade-in {
  animation: lazy-image-fade-in 0.3s ease-in-out;
}

/* Error state styling */
.lazy-image-error {
  background-color: #f8f8f8;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  text-align: center;
  padding: 10px;
}

/* Responsive image container */
.lazy-image-responsive {
  width: 100%;
  height: auto;
  aspect-ratio: 16/9; /* Default aspect ratio */
}

/* Menu item specific styling */
.menu-item-image {
  height: 100%;
  border-radius: 0.8rem 0 0 0.8rem;
}

/* Navbar logo specific styling */
.navbar-logo {
  width: 128px;
  height: auto;
}

/* Skeleton loading effect */
.lazy-image-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: lazy-image-skeleton 1.5s infinite;
}

@keyframes lazy-image-skeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .lazy-image-placeholder {
    background-color: #000;
    color: #fff;
  }
  
  .lazy-image-spinner {
    border-color: #fff;
    border-top-color: #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .lazy-image-spinner {
    animation: none;
  }
  
  .lazy-image {
    transition: none;
  }
  
  .lazy-image-skeleton {
    animation: none;
    background: #f0f0f0;
  }
}
