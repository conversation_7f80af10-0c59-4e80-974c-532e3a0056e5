import express from "express";
import cors from "cors";
import cookieParser from "cookie-parser";
import helmet from "helmet";
import compression from "compression";
import rateLimit from "express-rate-limit";
import morgan from "morgan";
import GlobelError from "./middlewares/error.middleware.js";
import {
  CORS_ORIGIN,
  IS_PRODUCTION,
  RATE_LIMIT_WINDOW_MS,
  RATE_LIMIT_MAX_REQUESTS,
  TRUST_PROXY,
  NODE_ENV
} from "./config/env.js";

// Routers
import healthRoutes from "./routes/health.routes.js";
import authRouters from "./routes/auth.routes.js";
import menuItemRouters from "./routes/menuItem.route.js";
import orderRouters from "./routes/order.routes.js";
import couponRouters from "./routes/coupon.routes.js";

const app = express();

// Trust proxy in production (for rate limiting, etc.)
if (IS_PRODUCTION && TRUST_PROXY) {
  app.set('trust proxy', 1);
}

// Health check endpoint - before any middleware
app.use('/health', healthRoutes);

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: IS_PRODUCTION ? undefined : false
}));

// Rate limiting
if (IS_PRODUCTION) {
  const limiter = rateLimit({
    windowMs: RATE_LIMIT_WINDOW_MS || 15 * 60 * 1000,
    max: RATE_LIMIT_MAX_REQUESTS || 100
  });
  app.use(limiter);
}

// Compression
app.use(compression());

// Logging
if (IS_PRODUCTION) {
  app.use(morgan('combined'));
} else {
  app.use(morgan('dev'));
}

// Parse CORS_ORIGIN for multiple origins
const baseOrigins = CORS_ORIGIN
  ? CORS_ORIGIN.split(',').map(origin => origin.trim())
  : [];

// Development origins
const devOrigins = [
  'http://localhost:3000',
  'http://localhost:3001',
  'http://localhost:5173',
  'http://localhost:5174'
];

// Combine origins and remove duplicates
const allowedOrigins = [...new Set([
  ...baseOrigins,
  ...(IS_PRODUCTION ? [] : devOrigins)
])];

// CORS configuration
const corsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps, curl, Postman)
    if (!origin) {
      return callback(null, true);
    }

    // In development, allow all origins for testing
    if (!IS_PRODUCTION) {
      console.log(`🌐 CORS allowing origin: ${origin} (development mode)`);
      return callback(null, true);
    }

    // Check if origin is in allowed list (exact match or starts with allowed origin)
    const isAllowed = allowedOrigins.some(allowed =>
      origin === allowed || origin.startsWith(allowed)
    );

    if (isAllowed) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked origin: ${origin}`);
      console.log(`Allowed origins: ${allowedOrigins.join(', ')}`);
      callback(new Error(`Not allowed by CORS. Origin: ${origin}`));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  maxAge: 86400 // 24 hours
};

app.use(cors(corsOptions));

// Debug CORS configuration
if (!IS_PRODUCTION) {
  console.log('🌐 CORS Configuration:');
  console.log(`   Allowed Origins: ${allowedOrigins.join(', ')}`);
  console.log(`   Environment: ${NODE_ENV}`);
}

// Basic middleware
app.use(cookieParser());
app.use(express.json({ limit: "24kb" }));
app.use(express.urlencoded({ extended: true, limit: "16kb" }));

// Request logging middleware (production only)
if (IS_PRODUCTION) {
  app.use((req, res, next) => {
    console.log(`${req.method} ${req.path} from ${req.get('origin') || 'no-origin'}`);
    next();
  });
}

// API routes
app.use('/api/auth', authRouters);
app.use('/api/menu', menuItemRouters);
app.use('/api/orders', orderRouters);
app.use('/api/coupons', couponRouters);

// Invalid route handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found",
    path: req.path
  });
});

// Global error handler
app.use(GlobelError);

export default app;
