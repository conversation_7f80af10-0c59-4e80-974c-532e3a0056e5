import nodemailer from 'nodemailer';

export async function validateEmail(config) {
  try {
    const transporter = nodemailer.createTransport({
      host: config.host,
      port: 587,
      auth: {
        user: config.user,
        pass: config.pass
      }
    });
    await transporter.verify();
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: `Email configuration failed: ${error.message}` 
    };
  }
}
