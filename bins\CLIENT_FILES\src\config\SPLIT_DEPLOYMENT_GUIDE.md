# 🚀 Split Deployment Guide

## 📋 Overview

To solve the memory issues on <PERSON>der, we're splitting the monorepo into two separate repositories:

1. **Backend (Server)** → Deploy on **Render**
2. **Frontend (Client)** → Deploy on **Vercel**

This approach will:
- ✅ Reduce memory usage during builds
- ✅ Enable independent deployments
- ✅ Optimize each service for its platform
- ✅ Improve build performance

---

## 🔧 **Step 1: Create Backend Repository**

### **1.1 Create New Repository**
```bash
# Create new repository on GitHub
# Name: saffron-saga-server
```

### **1.2 Setup Server Code**
```bash
# Create new directory
mkdir saffron-saga-server
cd saffron-saga-server

# Initialize git
git init
git remote add origin https://github.com/yourusername/saffron-saga-server.git

# Copy server files from current project
cp -r ../saffron-saga/server/* .
cp ../saffron-saga/server-only/* .

# Copy necessary root files
cp ../saffron-saga/.nvmrc .
cp ../saffron-saga/.npmrc .
```

### **1.3 Deploy to Render**
1. **Connect Repository** to Render
2. **Use Blueprint** with the included `render.yaml`
3. **Create Environment Group** `saffron-saga-secrets`:
   - `MONGODB_URI`
   - `DB_NAME`
   - `BREVO_EMAIL`
   - `BREVO_USER`
   - `BREVO_PASS`
   - `RESTAURANT_EMAIL`

### **1.4 Update CORS Origin**
After frontend deployment, update `CORS_ORIGIN` to:
```
https://saffron-saga-client.vercel.app
```

---

## 🌐 **Step 2: Create Frontend Repository**

### **2.1 Create New Repository**
```bash
# Create new repository on GitHub
# Name: saffron-saga-client
```

### **2.2 Setup Client Code**
```bash
# Create new directory
mkdir saffron-saga-client
cd saffron-saga-client

# Initialize git
git init
git remote add origin https://github.com/yourusername/saffron-saga-client.git

# Copy client files from current project
cp -r ../saffron-saga/client/* .
cp ../saffron-saga/client-only/* .
```

### **2.3 Update API Configuration**
Update `src/config/api.js`:
```javascript
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://saffron-saga-server.onrender.com';
```

### **2.4 Deploy to Vercel**
1. **Connect Repository** to Vercel
2. **Framework Preset:** Vite
3. **Build Command:** `npm run build:prod`
4. **Output Directory:** `dist`
5. **Environment Variables:**
   - `VITE_API_BASE_URL=https://saffron-saga-server.onrender.com`

---

## 🔄 **Step 3: Update Cross-References**

### **3.1 Backend CORS Update**
After frontend deployment, update backend environment:
```env
CORS_ORIGIN=https://saffron-saga-client.vercel.app
```

### **3.2 Frontend API Update**
Update frontend environment:
```env
VITE_API_BASE_URL=https://saffron-saga-server.onrender.com
```

---

## 📋 **Step 4: Verification Checklist**

### **Backend (Render)**
- [ ] Repository created and connected
- [ ] Environment variables configured
- [ ] Health check working: `https://saffron-saga-server.onrender.com/health`
- [ ] API endpoints responding
- [ ] Database connected
- [ ] CORS configured for frontend domain

### **Frontend (Vercel)**
- [ ] Repository created and connected
- [ ] Build successful
- [ ] Application loading: `https://saffron-saga-client.vercel.app`
- [ ] API calls working
- [ ] All pages accessible
- [ ] Forms submitting correctly

### **Integration**
- [ ] Frontend can reach backend API
- [ ] CORS allows frontend domain
- [ ] Orders can be placed successfully
- [ ] Admin panel functional
- [ ] Email notifications working

---

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **CORS Errors**
```javascript
// Backend: Update CORS_ORIGIN
CORS_ORIGIN=https://saffron-saga-client.vercel.app

// Frontend: Check API_BASE_URL
VITE_API_BASE_URL=https://saffron-saga-server.onrender.com
```

#### **API Connection Issues**
1. Verify backend health endpoint
2. Check environment variables
3. Ensure HTTPS in production URLs

#### **Build Failures**
1. Check Node.js versions match
2. Verify all dependencies installed
3. Review build logs for specific errors

---

## 📊 **Benefits of Split Deployment**

### **Performance**
- ✅ **Faster builds** - Each service builds independently
- ✅ **Lower memory usage** - No monorepo overhead
- ✅ **Optimized platforms** - Vercel for frontend, Render for backend

### **Development**
- ✅ **Independent deployments** - Deploy frontend/backend separately
- ✅ **Better CI/CD** - Focused build processes
- ✅ **Easier scaling** - Scale services independently

### **Maintenance**
- ✅ **Cleaner repositories** - Single responsibility
- ✅ **Focused dependencies** - Only what each service needs
- ✅ **Better organization** - Clear separation of concerns

---

## 🎯 **Final URLs**

After successful deployment:

- **Frontend:** `https://saffron-saga-client.vercel.app`
- **Backend:** `https://saffron-saga-server.onrender.com`
- **API Health:** `https://saffron-saga-server.onrender.com/health`

**🎉 Your application will now deploy successfully without memory issues!**
