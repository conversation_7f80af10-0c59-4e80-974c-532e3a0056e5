import axios from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

// Environment info
export const IS_PRODUCTION = import.meta.env.VITE_NODE_ENV === 'production';
export const IS_DEVELOPMENT = import.meta.env.VITE_NODE_ENV === 'development';

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: IS_PRODUCTION ? 30000 : 10000, // 30s in production, 10s in development
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  withCredentials: true, // Include cookies for authentication
});

// Request interceptor for adding auth tokens and logging
apiClient.interceptors.request.use(
  (config) => {
    // Add timestamp for debugging
    config.metadata = { startTime: new Date() };

    // Log requests in development
    if (IS_DEVELOPMENT) {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    }

    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and logging
apiClient.interceptors.response.use(
  (response) => {
    // Calculate request duration
    const duration = new Date() - response.config.metadata.startTime;

    // Log successful responses in development
    if (IS_DEVELOPMENT) {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);
    }

    return response;
  },
  (error) => {
    // Calculate request duration
    const duration = error.config?.metadata ? new Date() - error.config.metadata.startTime : 0;

    // Enhanced error logging
    if (error.response) {
      // Server responded with error status
      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    } else if (error.request) {
      // Request was made but no response received
      console.error(`🔌 Network Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        message: 'No response received from server',
        timeout: error.code === 'ECONNABORTED',
      });
    } else {
      // Something else happened
      console.error('⚠️ Request Setup Error:', error.message);
    }

    return Promise.reject(error);
  }
);

// API Endpoints - Updated to match server routes
export const API_ENDPOINTS = {
  // Health check
  HEALTH: '/health',
  HEALTH_DETAILED: '/health?validate=true',

  // Menu endpoints
  MENU_ITEMS: '/api/menu',
  CREATE_MENU_ITEM: '/api/menu/create',
  EDIT_MENU_AVAILABILITY: '/api/menu/edit-avilability',

  // Order endpoints
  CREATE_ORDER: '/api/orders/create',

  // Auth endpoints
  ADMIN_REGISTER: '/api/auth/admin/register',
  ADMIN_LOGIN: '/api/auth/admin/login',
  ADMIN_LOGOUT: '/api/auth/admin/logout',
};

// Export configured axios instance
export { apiClient };

// Export base URL for any custom endpoints
export { API_BASE_URL };
