#!/usr/bin/env node

/**
 * Quick seed script to add menu items
 */

import mongoose from 'mongoose';
import { MONGODB_URI, DB_NAME } from '../config/env.js';
import { MenuItem } from '../models/menuItem.model.js';

async function quickSeed() {
  try {
    console.log('🌱 Quick seeding menu items...');
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      dbName: DB_NAME,
      serverSelectionTimeoutMS: 5000,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // Check if items already exist
    const count = await MenuItem.countDocuments();
    console.log(`📊 Found ${count} existing menu items`);
    
    if (count === 0) {
      // Add sample items
      const items = [
        {
          name: 'butter chicken',
          description: 'creamy tomato-based curry with tender chicken',
          category: 'main course',
          price: 350,
          quantity: 1,
          unit: 'plate',
          isAvailable: true
        },
        {
          name: 'paneer tikka',
          description: 'grilled cottage cheese with spices',
          category: 'main course',
          price: 320,
          quantity: 1,
          unit: 'plate',
          isAvailable: true
        },
        {
          name: 'naan bread',
          description: 'soft indian bread',
          category: 'bread',
          price: 60,
          quantity: 1,
          unit: 'piece',
          isAvailable: true
        }
      ];
      
      const result = await MenuItem.insertMany(items);
      console.log(`✅ Added ${result.length} menu items`);
    } else {
      console.log('ℹ️  Menu items already exist');
    }
    
    // List all items
    const allItems = await MenuItem.find();
    console.log('📋 Current menu items:');
    allItems.forEach(item => {
      console.log(`  - ${item.name} (₹${item.price})`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Connection closed');
  }
}

quickSeed();
