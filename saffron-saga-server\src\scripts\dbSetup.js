#!/usr/bin/env node

/**
 * Database Setup Script for Production Deployment
 * Runs before the application starts to ensure database is ready
 */

import mongoose from 'mongoose';
import { MONGODB_URI, DB_NAME, IS_PRODUCTION } from '../config/env.js';

// Import models to ensure they're registered
import { Admin } from '../models/admin.model.js';
import { MenuItem } from '../models/menuItem.model.js';
import { Order } from '../models/order.model.js';

/**
 * Setup database connection and indexes
 */
async function setupDatabase() {
  try {
    console.log('🔧 Starting database setup...');
    
    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI, {
      dbName: DB_NAME,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    
    console.log('✅ Connected to MongoDB successfully');
    
    // Ensure indexes are created
    console.log('📊 Creating database indexes...');
    await createIndexes();
    
    // Verify collections exist
    console.log('🔍 Verifying collections...');
    await verifyCollections();
    
    // Create default admin if none exists (production safety)
    if (IS_PRODUCTION) {
      console.log('👤 Checking for admin users...');
      await ensureAdminExists();
    }
    
    console.log('🎉 Database setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    throw error;
  } finally {
    // Close connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

/**
 * Create necessary database indexes for performance
 */
async function createIndexes() {
  try {
    // Admin model indexes
    await Admin.collection.createIndex({ email: 1 }, { unique: true });
    console.log('  ✅ Admin email index created');
    
    // MenuItem model indexes
    await MenuItem.collection.createIndex({ name: 1 });
    await MenuItem.collection.createIndex({ category: 1 });
    await MenuItem.collection.createIndex({ isAvailable: 1 });
    await MenuItem.collection.createIndex({ price: 1 });
    console.log('  ✅ MenuItem indexes created');
    
    // Order model indexes
    await Order.collection.createIndex({ createdAt: -1 });
    await Order.collection.createIndex({ status: 1 });
    await Order.collection.createIndex({ 'address.phone_number': 1 });
    console.log('  ✅ Order indexes created');
    
  } catch (error) {
    // Index creation errors are usually not critical (might already exist)
    console.warn('⚠️  Index creation warning:', error.message);
  }
}

/**
 * Verify that all required collections exist
 */
async function verifyCollections() {
  const db = mongoose.connection.db;
  const collections = await db.listCollections().toArray();
  const collectionNames = collections.map(col => col.name);
  
  const requiredCollections = ['admins', 'menuitems', 'orders'];
  
  for (const collection of requiredCollections) {
    if (collectionNames.includes(collection)) {
      console.log(`  ✅ Collection '${collection}' exists`);
    } else {
      console.log(`  ℹ️  Collection '${collection}' will be created on first use`);
    }
  }
}

/**
 * Ensure at least one admin exists in production
 * This is a safety measure for production deployments
 */
async function ensureAdminExists() {
  try {
    const adminCount = await Admin.countDocuments();
    
    if (adminCount === 0) {
      console.log('  ⚠️  No admin users found in production database');
      console.log('  ℹ️  Please create an admin user through the registration endpoint');
      console.log('  📝 POST /api/auth/admin/register');
    } else {
      console.log(`  ✅ Found ${adminCount} admin user(s)`);
    }
  } catch (error) {
    console.warn('  ⚠️  Could not verify admin users:', error.message);
  }
}

/**
 * Handle script execution
 */
async function main() {
  try {
    await setupDatabase();
    process.exit(0);
  } catch (error) {
    console.error('💥 Database setup script failed:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { setupDatabase };
