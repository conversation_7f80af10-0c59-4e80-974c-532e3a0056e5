import dotenv from "dotenv";
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Make sure NODE_ENV is set before loading config
export const NODE_ENV = process.env.NODE_ENV || 'development';
export const IS_PRODUCTION = NODE_ENV === 'production';

// Load environment-specific config
const envFile = IS_PRODUCTION ? '.env.production' : '.env';
const envPath = resolve(__dirname, '../..', envFile);

// Load environment variables (optional in production)
const envResult = dotenv.config({ path: envPath });

// Only throw error if .env file is missing in development
if (envResult.error && !IS_PRODUCTION) {
    throw new Error(`Environment configuration error: ${envResult.error.message}`);
}

// In production, environment variables should be set by the platform
if (IS_PRODUCTION && envResult.error) {
    console.log('ℹ️  No .env file found in production - using platform environment variables');
}

// Load and validate critical environment variables
const requiredEnvVars = [
    'MONGODB_URI',
    'DB_NAME',
    'JWT_SECRET'
];

// CORS_ORIGIN is optional - will default to localhost in development
const optionalEnvVars = [
    'CORS_ORIGIN',
    'BREVO_EMAIL',
    'BREVO_USER',
    'BREVO_PASS',
    'RESTAURANT_EMAIL'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
}

// Log missing optional variables in development
if (!IS_PRODUCTION) {
    const missingOptional = optionalEnvVars.filter(varName => !process.env[varName]);
    if (missingOptional.length > 0) {
        console.warn(`⚠️  Missing optional environment variables: ${missingOptional.join(', ')}`);
    }
}

// Export environment variables with defaults
export const PORT = parseInt(process.env.PORT || '8080', 10);
export const MONGODB_URI = process.env.MONGODB_URI;
export const DB_NAME = process.env.DB_NAME;
export const CORS_ORIGIN = process.env.CORS_ORIGIN || (IS_PRODUCTION ? '*' : 'http://localhost:3000');
export const JWT_SECRET = process.env.JWT_SECRET;
export const SESSION_SECRET = process.env.SESSION_SECRET || process.env.JWT_SECRET;
export const SALT_ROUNDS = parseInt(process.env.SALT_ROUNDS || '12', 10);
export const BREVO_EMAIL = process.env.BREVO_EMAIL || '';
export const BREVO_USER = process.env.BREVO_USER || '';
export const BREVO_PASS = process.env.BREVO_PASS || '';
export const RESTAURANT_EMAIL = process.env.RESTAURANT_EMAIL || process.env.BREVO_EMAIL || '';
export const RATE_LIMIT_WINDOW_MS = parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10);
export const RATE_LIMIT_MAX_REQUESTS = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10);
export const TRUST_PROXY = process.env.TRUST_PROXY === 'true' || IS_PRODUCTION;
