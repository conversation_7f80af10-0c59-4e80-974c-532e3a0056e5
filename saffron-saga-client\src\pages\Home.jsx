import React, { useContext } from "react";
import Navbar from "../components/Navbar";
import "./home.css";
import MenuItem from "../components/MenuItem";
import { MenuContext } from "../context/MenuContext";
import { CartContext } from "../context/CartContext";
import { useNavigate } from "react-router-dom";
import BannerCarousel from "../components/BannerCarousel";

const Home = () => {
  const navigate = useNavigate();

  const { menuItems } = useContext(MenuContext);
  const { cart } = useContext(CartContext);

  const total = cart.reduce((sum, cartItem) => {
    const menuItem = menuItems.find((item) => item._id === cartItem._id);
    return sum + (menuItem?.price || 0) * cartItem.quantity;
  }, 0);

  const handleClick = () => {
    navigate("/cart"); // navigate to About page
  };

  return (
    <>
      <div id="home-container">
        <Navbar />
        <div
          style={
            total > 0 ? { paddingBottom: "12.5rem" } : { paddingBottom: "6rem" }
          }
          id="menu-container"
        >
          <BannerCarousel />
          <div className="menu-items-section">
            {menuItems.length === 0 ? (
              <h1 className="center">Loading...</h1>
            ) : (
              <MenuItem />
            )}
          </div>
        </div>
        {total > 0 && (
          <div className="bottom-bar">
            <div className="bottom-bar-left">
              <span>Total</span>
              <span className="price">
                {new Intl.NumberFormat("en-IN", {
                  style: "currency",
                  currency: "INR",
                }).format(total)}
              </span>
            </div>
            <div className="bottom-bar-right">
              <button onClick={handleClick} className="next-btn">
                Order Now →
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Home;
