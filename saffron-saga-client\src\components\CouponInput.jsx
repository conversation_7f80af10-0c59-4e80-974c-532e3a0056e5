import React, { useState } from 'react';
import couponService from '../services/couponService';
import './CouponInput.css';

const CouponInput = ({ 
  orderAmount, 
  onCouponApplied, 
  onCouponRemoved, 
  appliedCoupon = null,
  userId = null,
  disabled = false 
}) => {
  const [couponCode, setCouponCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      setError('Please enter a coupon code');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await couponService.validateCoupon(
        couponCode.trim().toUpperCase(),
        orderAmount,
        userId
      );

      if (result.success) {
        onCouponApplied({
          code: couponCode.trim().toUpperCase(),
          ...result.data
        });
        setCouponCode('');
        setError('');
      } else {
        setError(result.message);
      }
    } catch (error) {
      setError('Failed to apply coupon. Please try again.');
      console.error('Coupon application error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveCoupon = () => {
    onCouponRemoved();
    setCouponCode('');
    setError('');
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleApplyCoupon();
    }
  };

  if (appliedCoupon) {
    return (
      <div className="coupon-applied">
        <div className="coupon-success">
          <div className="coupon-info">
            <i className="ri-checkbox-circle-fill success-icon"></i>
            <div>
              <span className="coupon-code">{appliedCoupon.coupon.code}</span>
              <span className="coupon-description">{appliedCoupon.coupon.description}</span>
            </div>
          </div>
          <div className="coupon-savings">
            <span className="savings-text">You saved ₹{appliedCoupon.savings}</span>
            <button 
              className="remove-coupon-btn"
              onClick={handleRemoveCoupon}
              disabled={disabled}
              title="Remove coupon"
            >
              <i className="ri-close-line"></i>
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="coupon-input-container">
      <div className="coupon-input-wrapper">
        <div className="input-group">
          <input
            type="text"
            placeholder="Enter coupon code"
            value={couponCode}
            onChange={(e) => {
              setCouponCode(e.target.value.toUpperCase());
              setError('');
            }}
            onKeyPress={handleKeyPress}
            className={`coupon-input ${error ? 'error' : ''}`}
            disabled={disabled || isLoading}
            maxLength={20}
          />
          <button
            className="apply-coupon-btn"
            onClick={handleApplyCoupon}
            disabled={disabled || isLoading || !couponCode.trim()}
          >
            {isLoading ? (
              <i className="ri-loader-4-line loading-icon"></i>
            ) : (
              'Apply'
            )}
          </button>
        </div>
        
        {error && (
          <div className="coupon-error">
            <i className="ri-error-warning-line"></i>
            <span>{error}</span>
          </div>
        )}
      </div>
      
      <div className="coupon-hint">
        <i className="ri-information-line"></i>
        <span>Have a coupon code? Enter it above to get discount!</span>
      </div>
    </div>
  );
};

export default CouponInput;
