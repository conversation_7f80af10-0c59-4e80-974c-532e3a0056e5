*,
*::before,
*::after {
  box-sizing: border-box;
}
* {
  margin: 0;
  padding: 0;
}
html,
body {
  height: 100%;
  width: 100%;
}
html {
  font-size: 62.5%;
}
:root {
  --font-c: #252525;
  --bg-c: #ffffff;
}
body {
  background-color: white;
}

.signup-container {
  margin-inline: auto;
  width: 100%;
  max-width: 500px;
  font-family: sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
   width: 100%;
  height: 100dvh;
  background-color: white;
}
.heading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.case-on-delivary {
  font-family: "Poppins", sans-serif;
  font-size: 0.8rem;
  line-height: 12px;
  font-weight: 400;
  font-style: normal;
}

.back-btn {
  align-self: flex-start;
  background: none;
  border: none;
  font-size: 1rem;
  color: black;
  display: flex;
  align-items: center;
  cursor: pointer;
}

 
.signup-form-container {
  margin-top: 1.4rem;
  width: 100%;
  padding-inline: 1.6rem;
  gap: 2rem;
  display: flex;
  align-items: center;
  background-color: white;
  flex-direction: column;
  overflow-y: scroll;
}
.signup-form-container form {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.signup-form-container form .input-filed {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}
.signup-form-container form .input-filed label {
  font-size: 1.2rem;
  line-height: 19.4px;
  font-weight: 400;
  font-family: inter;
}
.signup-form-container form .input-filed .input {
  height: 3.6rem;
  padding-inline: 0.8rem;
  border-radius: 0.4rem;
  background-color: #e3e2e2;
  border: 1px solid black;
}
.input-container {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  padding-bottom: 7.5rem;
}
.btn-container {
  width: 100%;
  position: relative;
}
.signup-form-container form button {
  margin-inline: auto;
  max-width: 470px;
  font-size: 1.4rem;
  line-height: 22.4px;
  font-weight: 600;
  font-family: inter;
  width: 90%;
  padding: 1.6rem;
  background-color: #ffdd00;
  border: none;
  position: fixed;
  bottom: 1.4rem;
  border-radius: 0.8rem;
}

.error {
  color: red;
  font-size: 0.8rem;
  margin-top: 4px;
}

label span {
  font-weight: bold;
}
