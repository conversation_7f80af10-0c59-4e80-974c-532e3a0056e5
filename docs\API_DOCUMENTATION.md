# Saffron Saga API Documentation

## Overview

The Saffron Saga API is a RESTful API built with Node.js, Express.js, and MongoDB. It provides endpoints for managing a restaurant's menu items, orders, and admin authentication.

**Base URL:** `https://saffron-saga-server.onrender.com`  
**API Version:** v1  
**Content-Type:** `application/json`

## Table of Contents

1. [Authentication](#authentication)
2. [Health Check](#health-check)
3. [Admin Authentication](#admin-authentication)
4. [Menu Items](#menu-items)
5. [Orders](#orders)
6. [Error Handling](#error-handling)
7. [Rate Limiting](#rate-limiting)
8. [Data Models](#data-models)

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Admin routes require a valid JWT token sent as an HTTP-only cookie named `accessToken`.

### Authentication Flow
1. Admin registers/logs in
2. Server returns JWT token as HTTP-only cookie
3. Subsequent requests include the cookie automatically
4. Protected routes verify the token

## Health Check

### GET /health

Check the server health status and optionally validate the complete setup.

**Parameters:**
- `validate` (query, optional): Set to `true` for detailed validation

**Response:**
```json
{
  "success": true,
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600,
  "memory": {
    "heapUsed": 50000000,
    "heapTotal": 100000000,
    "external": 5000000,
    "rss": 120000000
  },
  "environment": "production"
}
```

**With validation (`?validate=true`):**
```json
{
  "success": true,
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600,
  "memory": { ... },
  "environment": "production",
  "validation": {
    "database": { "status": "connected" },
    "email": { "status": "configured" },
    "routes": { "status": "available" },
    "env": { "status": "valid" }
  }
}
```

## Admin Authentication

### POST /api/auth/admin/register

Register a new admin user.

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

**Validation Rules:**
- `first_name`: Required, 2-50 characters
- `last_name`: Required, 2-50 characters  
- `email`: Required, valid email format, 10-150 characters
- `password`: Required, min 6 characters, must contain uppercase, lowercase, number, and special character

**Response (201):**
```json
{
  "success": true,
  "message": "Email registered successfully",
  "userId": "60f7b3b3b3b3b3b3b3b3b3b3"
}
```

**Error Response (409):**
```json
{
  "success": false,
  "message": "Email is already registered"
}
```

### POST /api/auth/admin/login

Authenticate an admin user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "first_name": "john",
    "last_name": "doe",
    "email": "<EMAIL>",
    "role": "admin",
    "isVerified": true,
    "full_name": "john doe"
  }
}
```

**Error Responses:**
- **404:** Invalid credentials or unauthorized admin
- **400:** Validation failed

### POST /api/auth/admin/logout

Logout the current admin (requires authentication).

**Headers:**
- Cookie: `accessToken=<jwt_token>`

**Response (200):**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

## Menu Items

### GET /api/menu

Get all menu items (public endpoint).

**Response (200):**
```json
{
  "success": true,
  "message": "Menu items retrieved successfully",
  "menuItems": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "name": "butter chicken",
      "description": "creamy tomato-based curry with tender chicken pieces",
      "category": "main course",
      "image": "https://example.com/image.jpg",
      "currency": "INR",
      "price": 350,
      "discount": 0,
      "discount_available": false,
      "quantity": 1,
      "unit": "plate",
      "isAvailable": true,
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

### POST /api/menu/create

Create a new menu item (requires admin authentication).

**Headers:**
- Cookie: `accessToken=<jwt_token>`

**Request Body:**
```json
{
  "name": "Butter Chicken",
  "description": "Creamy tomato-based curry with tender chicken pieces",
  "category": "Main Course",
  "price": 350,
  "quantity": 1,
  "unit": "plate"
}
```

**Validation Rules:**
- `name`: Required, 2-150 characters
- `description`: Required, 20-800 characters
- `category`: Required, 2-200 characters
- `price`: Required, positive number
- `quantity`: Required, positive number less than 100
- `unit`: Required, 2-30 characters

**Response (200):**
```json
{
  "success": true,
  "message": "MenuItem created Successfully",
  "menuId": "60f7b3b3b3b3b3b3b3b3b3b3"
}
```

### POST /api/menu/edit-avilability

Update menu item availability (requires admin authentication).

**Headers:**
- Cookie: `accessToken=<jwt_token>`

**Request Body:**
```json
{
  "menuId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "isAvailable": false
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "Menu item availability updated successfully"
}
```

## Orders

### POST /api/orders/create

Create a new order (public endpoint).

**Request Body:**
```json
{
  "cart": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "quantity": 2
    }
  ],
  "address": {
    "full_name": "John Doe",
    "phone_number": "9876543210",
    "pincode": 123456,
    "city": "BiharShrif",
    "street_road": "Main Street, Block A",
    "landmark": "Near City Mall"
  }
}
```

**Validation Rules:**

**Cart:**
- `cart`: Required, non-empty array
- `cart.*._id`: Required, valid MongoDB ObjectId
- `cart.*.quantity`: Required, integer >= 1

**Address:**
- `full_name`: Required, min 3 characters
- `phone_number`: Required, valid Indian mobile number (10 digits, starts with 6-9)
- `pincode`: Required, 6-digit number
- `city`: Required, min 2 characters
- `street_road`: Required, min 3 characters
- `landmark`: Optional, default empty string

**Response (201):**
```json
{
  "success": true,
  "message": "Order created successfully",
  "orderId": "60f7b3b3b3b3b3b3b3b3b3b3",
  "subtotal": 700
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Validation Failed",
  "errors": [
    {
      "field": "address.phone_number",
      "message": "Phone number is invalid"
    }
  ]
}
```

## Error Handling

The API uses consistent error response format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "field_name",
      "message": "Specific error message"
    }
  ]
}
```

### HTTP Status Codes

- **200:** Success
- **201:** Created
- **400:** Bad Request (validation errors)
- **401:** Unauthorized
- **404:** Not Found
- **409:** Conflict (duplicate resource)
- **429:** Too Many Requests (rate limit exceeded)
- **500:** Internal Server Error

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Window:** 15 minutes (900,000 ms)
- **Max Requests:** 100 per window per IP
- **Response Header:** `X-RateLimit-*` headers included

**Rate Limit Exceeded Response (429):**
```json
{
  "success": false,
  "message": "Too many requests, please try again later"
}
```

## Data Models

### Admin Model

```javascript
{
  "_id": "ObjectId",
  "first_name": "string (required, lowercase, trim)",
  "last_name": "string (required, lowercase, trim)",
  "email": "string (required, unique, lowercase, trim)",
  "password": "string (required, hashed, select: false)",
  "role": "string (default: 'user')",
  "isVerified": "boolean (default: false)",
  "createdAt": "Date",
  "updatedAt": "Date",
  "full_name": "string (virtual field)"
}
```

### MenuItem Model

```javascript
{
  "_id": "ObjectId",
  "name": "string (required, lowercase, trim)",
  "description": "string (required, lowercase, trim)",
  "category": "string (lowercase, trim, default: '')",
  "image": "string (optional)",
  "currency": "string (default: 'INR')",
  "price": "number (required)",
  "discount": "number (default: 0)",
  "discount_available": "boolean (default: false)",
  "quantity": "number (required, default: 1)",
  "unit": "string (required, default: 'pic')",
  "isAvailable": "boolean (default: true)",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### Order Model

```javascript
{
  "_id": "ObjectId",
  "cartId": ["ObjectId (ref: MenuItem, required)"],
  "cartItems": ["Object (required)"],
  "status": "string (enum: ['pending', 'confirmed', 'delivered'], default: 'pending')",
  "subtotal": "number (required)",
  "address": {
    "full_name": "string (required, trim)",
    "phone_number": "string (required, trim, lowercase)",
    "pincode": "number (required)",
    "city": "string (default: 'BiharShrif', trim, lowercase)",
    "street_road": "string (required, trim, lowercase)",
    "landmark": "string (default: '', trim, lowercase)"
  },
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### Cart Item Structure (in Order)

```javascript
{
  "_id": "ObjectId",
  "name": "string",
  "price": "number",
  "quantity": "number",
  "unit": "string",
  "total": "number"
}
```

## Security Features

### Password Security
- Passwords are hashed using bcrypt with configurable salt rounds
- Passwords are excluded from query results by default (`select: false`)

### JWT Security
- Tokens expire in 7 days
- Tokens are stored as HTTP-only cookies
- JWT secret is configurable via environment variables

### CORS Configuration
- Configurable CORS origins
- Credentials support enabled
- Pre-flight request handling

### Request Security
- Helmet.js for security headers
- Request size limits (JSON: 24kb, URL-encoded: 16kb)
- Rate limiting per IP address

## Environment Variables

Required environment variables for the API:

```bash
# Server Configuration
PORT=8080
NODE_ENV=production
MONGODB_URI=your_mongodb_uri_here
DB_NAME=saffron_saga
CORS_ORIGIN=https://your-frontend-domain.com

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
SALT_ROUNDS=10

# Email Configuration (for order notifications)
BREVO_EMAIL=your_brevo_email
BREVO_USER=your_brevo_user
BREVO_PASS=your_brevo_password
RESTAURANT_EMAIL=your_restaurant_email

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Security
SESSION_SECRET=your_session_secret
```

## API Testing Examples

### Using cURL

**Get Menu Items:**
```bash
curl -X GET https://saffron-saga-server.onrender.com/api/menu
```

**Admin Login:**
```bash
curl -X POST https://saffron-saga-server.onrender.com/api/auth/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"SecurePass123"}' \
  -c cookies.txt
```

**Create Menu Item (with authentication):**
```bash
curl -X POST https://saffron-saga-server.onrender.com/api/menu/create \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "name":"Butter Chicken",
    "description":"Creamy tomato-based curry with tender chicken pieces",
    "category":"Main Course",
    "price":350,
    "quantity":1,
    "unit":"plate"
  }'
```

**Create Order:**
```bash
curl -X POST https://saffron-saga-server.onrender.com/api/orders/create \
  -H "Content-Type: application/json" \
  -d '{
    "cart":[{"_id":"60f7b3b3b3b3b3b3b3b3b3b3","quantity":2}],
    "address":{
      "full_name":"John Doe",
      "phone_number":"9876543210",
      "pincode":123456,
      "city":"BiharShrif",
      "street_road":"Main Street, Block A",
      "landmark":"Near City Mall"
    }
  }'
```

## Support

For API support or questions, please contact the development team or refer to the project repository.
