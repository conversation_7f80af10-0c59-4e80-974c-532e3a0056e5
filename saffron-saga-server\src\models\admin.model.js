import mongoose from "mongoose";
import bcrypt from "bcrypt";
import { SALT_ROUNDS } from "../config/env.js";

const adminSchema = new mongoose.Schema(
  {
    first_name: {
      type: String,
      trim: true,
      lowercase: true,
      required: [true, "First name is required"],
    },
    last_name: {
      type: String,
      trim: true,
      lowercase: true,
      required: [true, "Last name is required"],
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
      unique: true,
      required: [true, "Email is required"],
      match: [
        /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/,
        "Please provide a valid email address",
      ],
    },
    password: {
      type: String,
      required: [true, "Password is required"],
      select: false,
    },
    role: {
      type: String,
      default: "user",
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtual full name
adminSchema.virtual("full_name").get(function () {
  return `${this.first_name} ${this.last_name}`;
});

// Hash password before save
adminSchema.pre("save", async function (next) {
  if (!this.isModified("password")) return next();

  try {
    this.password = await bcrypt.hash(this.password, Number(SALT_ROUNDS) || 10);
    next();
  } catch (error) {
    next(error);
  }
});

export const Admin = mongoose.model("Admin", adminSchema);
