import { validationResult } from "express-validator";
import { Admin } from "../models/admin.model.js";
import { JWT_SECRET } from "../config/env.js";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";

// Admin auth Controllers
// register admin controller
export const registerAdminController = async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation failed",
      errors: errors.array(),
    });
  }

  try {
    const { first_name, last_name, email, password } = req.body;

    // Check if user already exists
    const exist = await Admin.findOne({ email });
    if (exist) {
      return res.status(409).json({
        success: false,
        message: "Email is already registered",
      });
    }

    // Create new admin
    const newAdmin = await Admin.create({
      first_name,
      last_name,
      email,
      password,
    });

    // Send response
    return res.status(201).json({
      success: true,
      message: "<PERSON><PERSON> registered successfully",
      userId: newAdmin._id,
    });
  } catch (error) {
    next(error);
  }
};
// login admin controller
export const loginAdminController = async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation failed",
      errors: errors.array(),
    });
  }
  try {
    const { email, password } = req.body;

    const admin = await Admin.findOne({ email }).select("+password");
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Invalid Email and Password",
      });
    }

    if (admin.role !== "admin" || !admin.isVerified) {
      return res.status(404).json({
        success: false,
        message: "You are not Unauthorized as Admin",
      });
    }

    const match = await bcrypt.compare(password, admin.password);
    if (!match) {
      return res.status(400).json({
        success: false,
        message: "Invalid Email and Password",
      });
    }
    const token = jwt.sign({ userId: admin._id }, JWT_SECRET, {
      expiresIn: "7d",
    });

    res.cookie("accessToken", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production", // ensures HTTPS
      sameSite: "strict",
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });
    res.status(200).json({
      success: true,
      message: "Login successfully",
      data: {
        username: `${admin.first_name} ${admin.last_name}`,
        userId: admin._id,
      },
    });
  } catch (error) {
    next(error);
  }
};
// logout admin controller
export const logoutAdminController = async (req, res, next) => {
  try {
    res.clearCookie("accessToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production", // ensures HTTPS
      sameSite: "strict",
    });

    res.status(200).json({
      success: true,
      message: "Admin logout",
    });
  } catch (error) {
    next(error);
  }
};
