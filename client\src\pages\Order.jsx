import React, { useContext, useState } from "react";
import * as Yup from "yup";
import Navbar from "../components/Navbar";
import "./order.css";
import { useNavigate } from "react-router-dom";
import { CartContext } from "../context/CartContext";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { orderService } from "../services/api.js";

const addressSchema = Yup.object().shape({
  full_name: Yup.string()
    .required("Full name is required")
    .min(3, "Full name must be at least 3 characters"),
  phone_number: Yup.string()
    .required("Phone number is required")
    .matches(
      /^[6-9]\d{9}$/,
      "Phone number must be a valid Indian mobile number (10 digits starting with 6-9)"
    ),
  pincode: Yup.string()
    .required("Pincode is required")
    .matches(/^\d{6}$/, "Pincode must be exactly 6 digits"),
  city: Yup.string()
    .required("City is required")
    .min(2, "City must be at least 2 characters"),
  street_road: Yup.string()
    .required("Street/Road is required")
    .min(3, "Street must be at least 3 characters"),
  landmark: Yup.string()
    .required("Landmark is required")
    .min(3, "Landmark must be at least 3 characters"),
});

const initialAddressState = {
  full_name: "",
  phone_number: "",
  city: "",
  street_road: "",
  landmark: "",
  village_mohalla: "",
  pincode: "",
  house_number: "",
};

const Order = () => {
  const navigate = useNavigate();
  const [address, setAddress] = useState(initialAddressState);
  const { cart, setCart } = useContext(CartContext);
  const [errors, setErrors] = useState({});
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const [disable, setDisable] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setAddress((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const onSubmitHandle = async (e) => {
    e.preventDefault();
    setErrors({});

    try {
      await addressSchema.validate(address, { abortEarly: false });

      setDisable(true);
      setIsBtnLoading(true);

      // Prepare order data with proper formatting
      const orderData = {
        cart: cart.map(item => ({
          _id: item._id,
          quantity: parseInt(item.quantity) || 1
        })),
        address: {
          ...address,
          pincode: parseInt(address.pincode), // Convert to integer for server validation
          phone_number: address.phone_number.toString() // Ensure it's a string
        }
      };

      console.log('Sending order data:', orderData); // Debug log

      const result = await orderService.createOrder(orderData);

      if (result.success) {
        toast.success(result.message);
        navigate(`/order/confirm?orderId=${result.orderId}`);
        setAddress(initialAddressState);
        setTimeout(() => {
          setCart([]);
          setAddress(initialAddressState);
        }, 200);
      } else {
        // Handle server validation errors
        if (result.validationErrors && Array.isArray(result.validationErrors)) {
          const serverErrors = {};
          result.validationErrors.forEach((error) => {
            // Map server field names to client field names
            const field = error.path || error.param;
            if (field) {
              // Handle nested field names like 'address.full_name'
              const fieldName = field.includes(".")
                ? field.split(".")[1]
                : field;
              serverErrors[fieldName] = error.msg || error.message;
            }
          });
          setErrors(serverErrors);
          toast.error("Please fix the form errors and try again.");
        } else {
          toast.error(result.error);
        }
      }
    } catch (error) {
      console.error("Order submission error:", error);

      if (error.name === "ValidationError") {
        // Yup validation errors
        const formattedErrors = {};
        error.inner.forEach((err) => {
          formattedErrors[err.path] = err.message;
        });
        setErrors(formattedErrors);
        toast.error("Please fix the form errors and try again.");
      } else {
        toast.error("Something went wrong. Please try again.");
      }
    } finally {
      setIsBtnLoading(false);
      setDisable(false);
    }
  };

  const handleBack = () => navigate(-1);

  return (
    <section className="signup-container">
      <Navbar />
      <button onClick={handleBack} className="back-btn">
        <i className="ri-arrow-left-line"></i>Back
      </button>
      <div className="signup-form-container">
        <div className="heading-container">
          <h2 className="heading">Delivary Address</h2>
          <span className="case-on-delivary">Case on delivary</span>
        </div>

        <form onSubmit={onSubmitHandle}>
          <div className="input-container">
            {[
              { label: "Full Name", name: "full_name", type: "text" },
              {
                label: "Phone Number",
                name: "phone_number",
                type: "tel",
                maxLength: 10,
              },
              { label: "House Number", name: "house_number", type: "text" },
              { label: "Street/Road", name: "street_road", type: "text" },
              { label: "Landmark", name: "landmark", type: "text" },
              { label: "Village/Mohalla", name: "village_mohalla", type: "text" },
              { label: "City", name: "city", type: "text" },
              { label: "Pincode", name: "pincode", type: "text", maxLength: 6 },
            ].map(({ label, name, type, maxLength }) => (
              <div key={name} className="input-filed">
                <label htmlFor={name}>
                  {label}
                  {[
                    "full_name",
                    "phone_number",
                    "pincode",
                    "city",
                    "street_road",
                  ].includes(name) && <span style={{ color: "red" }}> *</span>}
                </label>

                <input
                  value={address[name]}
                  onChange={handleChange}
                  className="input"
                  type={type}
                  id={name}
                  name={name}
                  placeholder={`Enter ${label}`}
                  maxLength={maxLength}
                />
                {errors[name] && <p className="error">{errors[name]}</p>}
              </div>
            ))}
          </div>
          <div className="btn-container">
            <button disabled={disable} type="submit">
              {isBtnLoading ? "Confirming...." : "Confirm Order"}
            </button>
          </div>
        </form>
      </div>

      <ToastContainer position="top-center" autoClose={3000} />
    </section>
  );
};

export default Order;
