import express from 'express';
import { validateServerSetup } from '../utils/validation.js';

const router = express.Router();

router.get('/', async (req, res) => {
  try {
    const timestamp = new Date().toISOString();
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();

    // Basic health check
    const basicHealth = {
      success: true,
      status: 'healthy',
      timestamp,
      uptime,
      memory: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss
      },
      environment: process.env.NODE_ENV || 'development'
    };

    // Detailed validation if requested
    if (req.query.validate === 'true') {
      try {
        const validationResults = await validateServerSetup();
        res.status(200).json({
          ...basicHealth,
          validation: validationResults
        });
      } catch (error) {
        console.error('Health check validation error:', error);
        res.status(500).json({
          success: false,
          status: 'error',
          timestamp,
          error: error.message,
          ...basicHealth
        });
      }
    } else {
      res.status(200).json(basicHealth);
    }
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      success: false,
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

export default router;
