/* Coupon Input Container */
.coupon-input-container {
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.coupon-input-wrapper {
  margin-bottom: 0.5rem;
}

/* Input Group */
.input-group {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.coupon-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  background: white;
}

.coupon-input:focus {
  outline: none;
  border-color: #ff6b35;
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.coupon-input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.coupon-input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Apply <PERSON><PERSON> */
.apply-coupon-btn {
  padding: 0.75rem 1.5rem;
  background: #ff6b35;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.apply-coupon-btn:hover:not(:disabled) {
  background: #e55a2b;
  transform: translateY(-1px);
}

.apply-coupon-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Error Message */
.coupon-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

.coupon-error i {
  font-size: 1rem;
}

/* Coupon Hint */
.coupon-hint {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.8rem;
  font-style: italic;
}

.coupon-hint i {
  font-size: 0.9rem;
  color: #ff6b35;
}

/* Applied Coupon Styles */
.coupon-applied {
  margin: 1rem 0;
  padding: 1rem;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  border-radius: 8px;
}

.coupon-success {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.success-icon {
  color: #28a745;
  font-size: 1.2rem;
}

.coupon-code {
  font-weight: 700;
  font-size: 0.9rem;
  color: #155724;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: block;
}

.coupon-description {
  font-size: 0.8rem;
  color: #155724;
  opacity: 0.8;
  display: block;
  margin-top: 0.2rem;
}

.coupon-savings {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.savings-text {
  font-weight: 600;
  color: #28a745;
  font-size: 0.9rem;
}

.remove-coupon-btn {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.remove-coupon-btn:hover:not(:disabled) {
  background: rgba(108, 117, 125, 0.1);
  color: #495057;
}

.remove-coupon-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 480px) {
  .coupon-input-container {
    padding: 0.75rem;
    margin: 0.75rem 0;
  }
  
  .input-group {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .apply-coupon-btn {
    width: 100%;
    justify-content: center;
  }
  
  .coupon-success {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .coupon-savings {
    align-self: flex-end;
  }
}

/* Animation for coupon application */
.coupon-applied {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
