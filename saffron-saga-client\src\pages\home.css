*,
*::before,
*::after {
  box-sizing: border-box;
}
* {
  margin: 0;
  padding: 0;
}
html,
body {
  height: 100%;
  width: 100%;
}
html {
  font-size: 62.5%;
}
body {
  color: #000000;
}
:root {
  --font-c: #252525;
  --bg-c: #ffffff;
}
img {
  width: 100%;
}
h2 {
  font-size: 1.8rem;
  font-weight: 600;
  line-height: 27px;
}
.center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%);
}

#home-container {
  margin-inline: auto;
  width: 100%;
  max-width: 500px;
  height: 100dvh;
  overflow-y: hidden;
  background-color: #ffffff;
}
#menu-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  overflow-y: scroll;
  border-radius: 1rem;
  padding-top: 0.8rem;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Hide scrollbar for cleaner look */
#menu-container::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

#menu-container {
  scrollbar-width: none; /* Firefox */
}
