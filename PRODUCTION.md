# Saffron Saga - Production Quick Start

## 🚀 Quick Deployment on Render

This application is optimized for deployment on Render using the included `render.yaml` configuration.

### Prerequisites
- GitHub repository with your code
- [Render account](https://render.com) (free tier available)
- [MongoDB Atlas account](https://www.mongodb.com/cloud/atlas) (free tier available)
- [Brevo account](https://www.brevo.com) for email services (free tier available)

### 1. Environment Setup

Create environment variable group in Render dashboard named `saffron-saga-secrets` with:

```env
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/saffron_saga
DB_NAME=saffron_saga

# Email Service (Brevo)
BREVO_EMAIL=<EMAIL>
BREVO_USER=your-brevo-username
BREVO_PASS=your-brevo-smtp-password
RESTAURANT_EMAIL=<EMAIL>
```

### 2. Deploy with Blueprint

1. In Render dashboard, click "New +" → "Blueprint"
2. Connect your GitHub repository
3. Select the repository containing `render.yaml`
4. Click "Apply"

The `render.yaml` file will automatically:
- Deploy the backend API server
- Deploy the frontend React application
- Configure environment variables
- Set up health checks and caching

### 3. Verify Deployment

**Check API Health:**
```bash
curl https://saffron-saga-server.onrender.com/health
```

**Test Frontend:**
Visit: `https://saffron-saga-client.onrender.com`

## 📋 Production Features

### Security
- JWT authentication with HTTP-only cookies
- CORS protection
- Rate limiting (100 requests per 15 minutes)
- Input validation and sanitization
- Security headers with Helmet.js

### Performance
- Static asset caching (1 year for assets, no-cache for HTML)
- Gzip compression
- Optimized database queries
- CDN delivery through Render

### Monitoring
- Health check endpoint: `/health`
- Detailed validation: `/health?validate=true`
- Application logs in Render dashboard
- Automatic service restart on failures

## 🔧 Local Development

### Start Development Servers
```bash
# Start backend (from root)
npm run dev:server

# Start frontend (from root)
npm run dev:client
```

### Build for Production
```bash
# Build frontend
npm run build:client

# Build backend (no build step needed for Node.js)
npm run build:server
```

## 📚 Documentation

- **Complete API Documentation**: `docs/API_DOCUMENTATION.md`
- **Detailed Deployment Guide**: `docs/RENDER_DEPLOYMENT_GUIDE.md`

## 🆘 Quick Troubleshooting

### Common Issues

**Build Failures:**
- Check Node.js version (requires >=18.0.0)
- Verify all dependencies in package.json files
- Check build logs in Render dashboard

**Database Connection:**
- Verify MongoDB connection string
- Ensure IP whitelist includes 0.0.0.0/0 in MongoDB Atlas
- Check database user permissions

**CORS Errors:**
- Verify `CORS_ORIGIN` matches your frontend URL
- Ensure both services are deployed and running

**Email Not Working:**
- Verify Brevo SMTP credentials
- Check sender email is verified in Brevo
- Review server logs for email errors

### Support Resources
- [Render Documentation](https://render.com/docs)
- [MongoDB Atlas Docs](https://docs.atlas.mongodb.com/)
- [API Documentation](docs/API_DOCUMENTATION.md)
- [Deployment Guide](docs/RENDER_DEPLOYMENT_GUIDE.md)

## 🔄 Updates and Maintenance

### Automatic Deployments
- Pushes to `main` branch trigger automatic deployments
- Both frontend and backend update simultaneously
- Zero-downtime deployments on Render

### Manual Deployment
1. Push changes to GitHub
2. Render automatically detects and deploys
3. Monitor deployment in Render dashboard

### Health Monitoring
- Render monitors `/health` endpoint automatically
- Set up external monitoring for production alerts
- Review logs regularly for performance insights
