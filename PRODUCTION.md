# Saffron Saga Production Deployment Guide

## Production Setup

### Prerequisites
- Node.js >= 18.0.0
- MongoDB Atlas account
- Brevo (for email services)
- Render.com account

### Environment Variables

#### API Service
```env
NODE_ENV=production
PORT=8080
MONGODB_URI=your_mongodb_atlas_uri
JWT_SECRET=your_jwt_secret
BREVO_EMAIL=your_brevo_email
BREVO_USER=your_brevo_user
BREVO_PASS=your_brevo_pass
RESTAURANT_EMAIL=your_restaurant_email
HEALTH_CHECK_API_KEY=your_health_check_key
```

#### Client Service
```env
VITE_API_URL=https://saffron-saga-api.onrender.com
```

### Deployment Steps

1. **Initial Setup**
```bash
# Install production dependencies
npm run install:prod
```

2. **Build**
```bash
# Build both client and server
npm run build
```

3. **Validation**
```bash
# Run deployment validation
npm run validate
```

### Render.com Deployment

1. Fork/clone the repository to your GitHub account
2. Connect your repository to Render
3. Create two services:
   - Web Service (API)
   - Static Site (Client)
4. Configure environment variables in Render dashboard
5. Deploy!

### Health Monitoring

- Basic health check: `GET /api/health`
- Detailed health check: `GET /api/health/detailed`
  - Requires `X-API-KEY` header

### Production Optimizations

1. **Caching**
   - Static assets cached for 1 year
   - API responses cached appropriately
   - Database queries optimized

2. **Security**
   - CORS configured
   - Rate limiting enabled
   - Security headers set
   - Environment variables protected

3. **Performance**
   - Code splitting enabled
   - Assets optimized
   - Gzip compression
   - Database indexes created

### Monitoring & Maintenance

1. **Health Checks**
   - Monitor `/api/health` endpoint
   - Set up uptime monitoring
   - Configure alerts

2. **Database**
   - Regular backups
   - Index optimization
   - Connection pooling

3. **Logging**
   - Application logs in Render dashboard
   - Error tracking
   - Performance monitoring

### Troubleshooting

1. **API Issues**
   - Check health endpoint
   - Verify environment variables
   - Check MongoDB connection
   - Review API logs

2. **Client Issues**
   - Verify build output
   - Check API connection
   - Review browser console
   - Clear CDN cache

3. **Database Issues**
   - Check connection string
   - Verify network access
   - Review MongoDB logs

### Backup & Recovery

1. **Database Backups**
   - Daily automated backups
   - Manual backup before updates
   - Backup verification process

2. **Code Backups**
   - GitHub repository
   - Protected branches
   - Release tagging

3. **Environment Backups**
   - Document all environment variables
   - Store secure copies of credentials
   - Regular audit of security settings

### Scaling Considerations

1. **Database**
   - Connection pooling configured
   - Indexes optimized
   - Caching implemented

2. **API**
   - Horizontal scaling enabled
   - Load balancing configured
   - Rate limiting set

3. **Static Content**
   - CDN enabled
   - Asset optimization
   - Cache policies set
