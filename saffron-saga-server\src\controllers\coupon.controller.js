import { validationResult } from "express-validator";
import { Coupon } from "../models/coupon.model.js";

// Create a new coupon (Admin only)
export const createCouponController = async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation Failed",
      errors: errors.array(),
    });
  }

  try {
    const {
      code,
      description,
      type,
      value,
      minimumOrderAmount,
      maximumDiscountAmount,
      startDate,
      endDate,
      usageLimit,
      perUserLimit
    } = req.body;

    // Check if coupon code already exists
    const existingCoupon = await Coupon.findOne({ code: code.toUpperCase() });
    if (existingCoupon) {
      return res.status(400).json({
        success: false,
        message: "Coupon code already exists"
      });
    }

    const coupon = await Coupon.create({
      code: code.toUpperCase(),
      description,
      type,
      value,
      minimumOrderAmount: minimumOrderAmount || 0,
      maximumDiscountAmount: maximumDiscountAmount || null,
      startDate: startDate || new Date(),
      endDate,
      usageLimit: usageLimit || null,
      perUserLimit: perUserLimit || 1,
      createdBy: req.admin._id
    });

    res.status(201).json({
      success: true,
      message: "Coupon created successfully",
      data: coupon
    });

  } catch (error) {
    console.error("Create coupon error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create coupon",
      error: error.message
    });
  }
};

// Get all coupons (Admin only)
export const getAllCouponsController = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status = 'all' } = req.query;
    
    let filter = {};
    
    // Filter by status
    if (status === 'active') {
      filter = {
        isActive: true,
        startDate: { $lte: new Date() },
        endDate: { $gte: new Date() }
      };
    } else if (status === 'expired') {
      filter = { endDate: { $lt: new Date() } };
    } else if (status === 'inactive') {
      filter = { isActive: false };
    }

    const coupons = await Coupon.find(filter)
      .populate('createdBy', 'first_name last_name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Coupon.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: coupons,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalCoupons: total,
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error("Get coupons error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch coupons",
      error: error.message
    });
  }
};

// Validate and apply coupon (Public)
export const validateCouponController = async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation Failed",
      errors: errors.array(),
    });
  }

  try {
    const { code, orderAmount, userId } = req.body;

    // Find the coupon
    const coupon = await Coupon.findValidCoupon(code);
    
    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: "Invalid or expired coupon code"
      });
    }

    // Check usage limits
    if (coupon.usageLimit && coupon.usedCount >= coupon.usageLimit) {
      return res.status(400).json({
        success: false,
        message: "Coupon usage limit exceeded"
      });
    }

    // Check per-user limit
    if (userId && !coupon.canUserUse(userId)) {
      return res.status(400).json({
        success: false,
        message: "You have already used this coupon the maximum number of times"
      });
    }

    // Check minimum order amount
    if (orderAmount < coupon.minimumOrderAmount) {
      return res.status(400).json({
        success: false,
        message: `Minimum order amount of ₹${coupon.minimumOrderAmount} required for this coupon`
      });
    }

    // Calculate discount
    const discountAmount = coupon.calculateDiscount(orderAmount);
    const finalAmount = orderAmount - discountAmount;

    res.status(200).json({
      success: true,
      message: "Coupon applied successfully",
      data: {
        coupon: {
          code: coupon.code,
          description: coupon.description,
          type: coupon.type,
          value: coupon.value
        },
        orderAmount,
        discountAmount,
        finalAmount,
        savings: discountAmount
      }
    });

  } catch (error) {
    console.error("Validate coupon error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to validate coupon",
      error: error.message
    });
  }
};

// Update coupon status (Admin only)
export const updateCouponStatusController = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    const coupon = await Coupon.findByIdAndUpdate(
      id,
      { isActive },
      { new: true }
    );

    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: "Coupon not found"
      });
    }

    res.status(200).json({
      success: true,
      message: `Coupon ${isActive ? 'activated' : 'deactivated'} successfully`,
      data: coupon
    });

  } catch (error) {
    console.error("Update coupon status error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update coupon status",
      error: error.message
    });
  }
};

// Delete coupon (Admin only)
export const deleteCouponController = async (req, res, next) => {
  try {
    const { id } = req.params;

    const coupon = await Coupon.findByIdAndDelete(id);

    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: "Coupon not found"
      });
    }

    res.status(200).json({
      success: true,
      message: "Coupon deleted successfully"
    });

  } catch (error) {
    console.error("Delete coupon error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete coupon",
      error: error.message
    });
  }
};
