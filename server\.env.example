# Server Configuration
PORT=8080
NODE_ENV=production
MONGODB_URI=your_mongodb_uri_here
DB_NAME=saffron_saga
CORS_ORIGIN=https://saffron-saga-frontend.onrender.com

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
SALT_ROUNDS=10

# Email Configuration
BREVO_EMAIL=your_brevo_email
BREVO_USER=your_brevo_user
BREVO_PASS=your_brevo_password
RESTAURANT_EMAIL=your_restaurant_email

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
