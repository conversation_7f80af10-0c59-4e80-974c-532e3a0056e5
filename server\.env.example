# Server Configuration
NODE_ENV=development
PORT=4000
MONGODB_URI=mongodb://localhost:27017/saffron_saga
DB_NAME=saffron_saga
CORS_ORIGIN=http://localhost:5173

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_here_min_32_chars
SALT_ROUNDS=10
SESSION_SECRET=your_session_secret_here

# Email Configuration (Brevo SMTP)
BREVO_EMAIL=<EMAIL>
BREVO_USER=your-brevo-username
BREVO_PASS=your-brevo-smtp-password
RESTAURANT_EMAIL=<EMAIL>

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Production URLs (for reference)
# CORS_ORIGIN=https://saffron-saga-client.onrender.com
# MONGODB_URI=mongodb+srv://username:<EMAIL>/saffron_saga
