import express from 'express';
import cors from 'cors';
import { MenuItem } from './models/menuItem.model.js';
import connectToDB from './db/db.js';

const app = express();
const PORT = 4002;

// CORS for development
app.use(cors({
  origin: 'http://localhost:5173',
  credentials: true
}));

app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  console.log('📊 Health check requested');
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Menu endpoint
app.get('/api/menu', async (req, res) => {
  try {
    console.log('📥 Menu request received');
    const menuItems = await MenuItem.find({ isAvailable: true });
    console.log(`📊 Returning ${menuItems.length} menu items`);
    
    res.json({
      success: true,
      message: "Fetched all menu items successfully",
      data: menuItems,
    });
  } catch (error) {
    console.error('❌ Menu error:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// Start server
async function start() {
  try {
    console.log('🔧 Starting simple server...');
    await connectToDB();
    
    app.listen(PORT, () => {
      console.log(`🚀 Simple server running on http://localhost:${PORT}`);
      console.log(`📊 Test: http://localhost:${PORT}/health`);
      console.log(`🍽️  Menu: http://localhost:${PORT}/api/menu`);
    });
  } catch (error) {
    console.error('❌ Startup error:', error);
  }
}

start();
