import React, { createContext, useState } from "react";

export const CartContext = createContext();

export const CartProvider = ({ children }) => {
  const [cart, setCart] = useState([]);
  const [appliedCoupon, setAppliedCoupon] = useState(null);

  // Helper function to clear coupon
  const clearCoupon = () => {
    setAppliedCoupon(null);
  };

  // Helper function to apply coupon
  const applyCoupon = (couponData) => {
    setAppliedCoupon(couponData);
  };

  // Helper function to calculate total with coupon
  const calculateTotal = (menuItems) => {
    const subtotal = cart.reduce((sum, cartItem) => {
      const menuItem = menuItems.find((item) => item._id === cartItem._id);
      return sum + (menuItem?.price || 0) * cartItem.quantity;
    }, 0);

    const discountAmount = appliedCoupon?.discountAmount || 0;
    const finalAmount = subtotal - discountAmount;

    return {
      subtotal,
      discountAmount,
      finalAmount,
      savings: discountAmount
    };
  };

  return (
    <CartContext.Provider value={{
      cart,
      setCart,
      appliedCoupon,
      setAppliedCoupon,
      clearCoupon,
      applyCoupon,
      calculateTotal
    }}>
      {children}
    </CartContext.Provider>
  );
};
