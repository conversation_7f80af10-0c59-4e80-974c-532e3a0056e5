import { validationResult } from "express-validator";
import { MenuItem } from "../models/menuItem.model.js";

export const createMenuItemController = async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation failed",
      errors: errors.array(),
    });
  }

  try {
    const { name, description, price, quantity, unit, category } = req.body;

    const item = await MenuItem.create({
      name,
      description,
      category,
      price,
      quantity,
      unit,
    });

    res.status(200).json({
      success: true,
      message: "MenuItem created Successfully",
      menuId: item._id,
    });
  } catch (error) {
    next(error);
  }
};

export const getAllMenuItemController = async (req, res, next) => {
  try {
    const MenuItemList = await MenuItem.find({ isAvailable: true });

    res.status(200).json({
      success: true,
      message: "Fetched all menu items successfully",
      data: MenuItemList,
    });
  } catch (error) {
    next(error);
  }
};

export const editMenuItemAvailability = async (req, res, next) => {
  const { itemId, isAvailable } = req.body;
  try {
    const item = await MenuItem.findById(itemId);
    if (!item) {
      return res.status(400).json({
        success: false,
        message: "Menu Item not find",
      });
    }

    item.isAvailable = isAvailable;
    await item.save();
    res.status(201).json({
      success: true,
      message: "item availability updated",
      isAvailable: item.isAvailable,
    });
  } catch (error) {
    next(error);
  }
};
