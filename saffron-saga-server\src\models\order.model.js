import mongoose from "mongoose";

// Address Schema
const addressSchema = mongoose.Schema({
  full_name: {
    type: String,
    required: true,
    trim: true,
  },
  phone_number: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
  },
  pincode: {
    type: Number,
    required: true,
  },
  city: {
    type: String,
    default: "BiharShrif",
    trim: true,
    lowercase: true,
  },
  street_road: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
  },
  landmark: {
    type: String,
    default: "",
    trim: true,
    lowercase: true,
  },
});

// Order Schema with embedded address
const orderSchema = mongoose.Schema(
  {
    cartId: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "MenuItem",
        required: true,
      },
    ],
    cartItems: [
      {
        type: Object,
        required: true,
      },
    ],
    status: {
      type: String,
      enum: ["pending", "confirmed", "delivered"],
      default: "pending",
    },
    subtotal: {
      type: Number,
      required: true,
    },
    address: {
      type: addressSchema,
      required: true,
    },
  },
  { timestamps: true }
);

export const Order = mongoose.model("Order", orderSchema);
