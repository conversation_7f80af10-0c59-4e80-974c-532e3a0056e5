import mongoose from "mongoose";

const menuItemSchema = mongoose.Schema(
  {
    name: {
      type: String,
      trim: true,
      lowercase: true,
      required: true,
    },
    description: {
      type: String,
      trim: true,
      lowercase: true,
      required: true,
    },
    category: {
      type: String,
      trim: true,
      lowercase: true,
      default: "",
    },
    image: {
      type: String,
    },
    currency: {
      type: String,
      default: "INR",
    },
    price: {
      type: Number,
      required: true,
    },
    discount: {
      type: Number,
      default: 0,
    },
    discount_available: {
      type: Boolean,
      default: false,
    },
    quantity: {
      type: Number,
      default: 1,
      required: true,
    },
    unit: {
      type: String,
      default: "pic",
      required: true,
    },
    isAvailable: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

export const MenuItem = mongoose.model("MenuItem", menuItemSchema);
