import { validateDatabase } from './validators/databaseValidator.js';
import { validateEmail } from './validators/emailValidator.js';
import { validateStaticFiles } from './validators/staticFilesValidator.js';
import { validateRoutes } from './validators/routesValidator.js';
import { validateEnv } from './validators/envValidator.js';

// Configuration constants
const REQUIRED_ASSETS = ['favicon.svg', 'logo.png', 'default-image.png'];
const REQUIRED_ENV_VARS = [
  'MONGODB_URI',
  'DB_NAME',
  'JWT_SECRET',
  'CORS_ORIGIN',
  'BREVO_EMAIL',
  'BREVO_USER',
  'BREVO_PASS',
  'RESTAURANT_EMAIL',
  'PORT',
  'NODE_ENV',
  'RATE_LIMIT_WINDOW_MS',
  'RATE_LIMIT_MAX_REQUESTS',
  'SESSION_SECRET'
];

const REQUIRED_ROUTES = [
  '/api/health',
  '/api/menu',
  '/api/auth',
  '/api/orders'
];

export async function validateServerSetup() {
  const results = {
    database: false,
    email: false,
    staticFiles: false,
    routes: false,
    envVars: false,
    errors: [],
    warnings: []
  };

  try {
    // Environment variables validation first
    const envResult = validateEnv(REQUIRED_ENV_VARS);
    results.envVars = envResult.success;
    if (!envResult.success) {
      results.errors.push(envResult.error);
      // If critical env vars are missing, stop further validation
      if (envResult.missingCritical) {
        return results;
      }
    }

    // Database validation
    const dbResult = await validateDatabase(process.env.MONGODB_URI);
    results.database = dbResult.success;
    if (!dbResult.success) {
      results.errors.push(dbResult.error);
    }

    // Email validation
    const emailResult = await validateEmail({
      host: process.env.BREVO_EMAIL,
      user: process.env.BREVO_USER,
      pass: process.env.BREVO_PASS
    });
    results.email = emailResult.success;
    if (!emailResult.success) {
      // Email issues are warnings in dev, errors in prod
      if (process.env.NODE_ENV === 'production') {
        results.errors.push(emailResult.error);
      } else {
        results.warnings.push(emailResult.error);
      }
    }

    // Static files validation
    const staticResult = await validateStaticFiles(REQUIRED_ASSETS);
    results.staticFiles = staticResult.success;
    if (!staticResult.success) {
      if (process.env.NODE_ENV === 'production') {
        results.errors.push(staticResult.error);
      } else {
        results.warnings.push(staticResult.error);
      }
    }

    // Routes validation
    const app = (await import('../app.js')).default;
    const routesResult = await validateRoutes(app, REQUIRED_ROUTES);
    results.routes = routesResult.success;
    if (!routesResult.success) {
      results.errors.push(routesResult.error);
    }

    // Security checks for production
    if (process.env.NODE_ENV === 'production') {
      if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
        results.errors.push('JWT_SECRET is not secure enough for production');
      }
      
      if (!process.env.SESSION_SECRET || process.env.SESSION_SECRET.length < 32) {
        results.errors.push('SESSION_SECRET is not secure enough for production');
      }
      
      if (!process.env.CORS_ORIGIN || process.env.CORS_ORIGIN === '*') {
        results.errors.push('CORS_ORIGIN should be explicitly defined in production');
      }
    }

    return results;
  } catch (error) {
    results.errors.push(`Validation failed: ${error.message}`);
    return results;
  }
}
