import React, { useContext } from "react";
import { Navigate } from "react-router-dom";
import { CartContext } from "../context/CartContext";
import { toast } from "react-toastify";

const ProtectedOrderRoute = ({ children }) => {
  const { cart } = useContext(CartContext);

  if (!cart || cart.length === 0) {
    toast.warning("Your cart is empty. Add items to continue.");
    return <Navigate to="/" replace />;
  }

  return children;
};

export default ProtectedOrderRoute;
