// utils/sendWhatsApp.js
import twilio from "twilio";
import { TWILIO_AUTH, TWILIO_SID, RESTAURANT_PHONE } from "../config/env.js";

const client = twilio(TWILIO_SID, TWILIO_AUTH);

export const sendWhatsAppMessage = async (
  order,
  address,
  detailedItems,
  subtotal
) => {
  const itemsList = detailedItems
    .map((item) => `• ${item.name} × ${item.quantity} - ₹${item.price}`)
    .join("\n");

  const message = `
📦 *New Order Received*
🧾 *Order ID:* ${order._id}

👤 *Customer:* ${address.full_name}
📞 *Contact:* ${address.phone_number}
🏠 *Address:* ${address.landmark}, ${address.village_mohalla}, ${address.city} - ${address.pincode}

🍽️ *Order Details:*
${itemsList}

💰 *Subtotal:* ₹${subtotal}
  `;

  return client.messages.create({
    from: "whatsapp:+14155238886", // Twilio Sandbox
    to: `whatsapp:${ RESTAURANT_PHONE}`,
    body: message,
  });
};
