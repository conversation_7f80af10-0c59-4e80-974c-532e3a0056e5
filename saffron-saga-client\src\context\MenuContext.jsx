import { createContext, useEffect, useState } from "react";
import { menuService } from "../services/api.js";
import { toast } from "react-toastify";

export const MenuContext = createContext();

export const MenuProvider = ({ children }) => {
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchMenu = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await menuService.getMenuItems();

      if (result.success) {
        setMenuItems(result.data);
      } else {
        setError(result.error);
        setMenuItems([]);
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error fetching menu:", error);
      setError('Failed to load menu');
      setMenuItems([]);
      toast.error('Unable to connect to server. Please check your internet connection.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMenu();
  }, []);

  return (
    <MenuContext.Provider value={{ menuItems, loading, error, refetch: fetchMenu }}>
      {children}
    </MenuContext.Provider>
  );
};
