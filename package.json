{"name": "saffron-saga", "version": "1.0.0", "description": "Fullstack Saffron Saga Restaurant Application", "private": true, "scripts": {"install:prod": "npm ci && cd client && npm ci && cd ../server && npm ci", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start:prod": "cd server && npm run start:prod", "validate": "node scripts/validate-deployment.js", "clean": "rimraf **/dist **/.cache"}, "dependencies": {"axios": "^1.6.2", "dotenv": "^16.3.1", "mongoose": "^8.15.1"}, "engines": {"node": ">=18.0.0"}, "keywords": ["restaurant", "food-ordering", "fullstack", "mern"], "author": "", "license": "ISC"}