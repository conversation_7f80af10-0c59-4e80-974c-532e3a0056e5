import { validationResult } from "express-validator";
import { Order } from "../models/order.model.js";
import { calculateSubtotal } from "../utils/orderHelper.js";
import { sendEmail } from "../utils/email.js";
import { RESTAURANT_EMAIL } from "../config/env.js";
import { sendWhatsAppMessage } from "../utils/sendWhatsApp.js";

export const createOrderController = async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation Failed",
      errors: errors.array(),
    });
  }

  try {
    const { cart, address } = req.body;
    const { subtotal, detailedItems, ids } = await calculateSubtotal(cart);

    const order = await Order.create({
      cartId: ids,
      cartItems: detailedItems,
      subtotal,
      address,
    });

    // Prepare email content for restaurant
    const itemsList = detailedItems
      .map(
        (item) =>
          `<li>${item.name} - Quantity: ${item.quantity} - Price: ₹${item.price}</li>`
      )
      .join("");

    const emailHtml = `
      <h2>New Order Received</h2>
      <p><strong>Order ID:</strong> ${order._id}</p>
      <p><strong>Customer:</strong> ${address.full_name}</p>
      <p><strong>Contact:</strong> ${address.phone_number}</p>
      <p><strong>Address:</strong> ${address.landmark}, ${address.village_mohalla}, ${address.city} - ${address.pincode}</p>
      <h3>Order Details:</h3>
      <ul>${itemsList}</ul>
      <p><strong>Subtotal:</strong> ₹${subtotal}</p>
    `;

    // Send email to restaurant
    await sendEmail({
      to: RESTAURANT_EMAIL,
      subject: `New Order Received - ${order._id}`,
      text: `You have a new order from ${address.full_name}, total ₹${subtotal}.`,
      html: emailHtml,
    });

    // Send WhatsApp to restaurant (non-blocking)
    try {
      await sendWhatsAppMessage(order, address, detailedItems, subtotal);
    } catch (whatsappError) {
      console.error("❌ WhatsApp Message Failed:", whatsappError.message);
    }

    // Send success response
    res.status(201).json({
      success: true,
      message: "Order confirmed",
      orderId: order._id,
    });
  } catch (error) {
    console.error("❌ ORDER CREATION FAILED:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create order",
      error: error.message,
    });
  }
};
