import { validationResult } from "express-validator";
import { Order } from "../models/order.model.js";
import { Coupon } from "../models/coupon.model.js";
import { calculateSubtotal } from "../utils/orderHelper.js";
import { sendEmail } from "../utils/email.js";
import { RESTAURANT_EMAIL } from "../config/env.js";

export const createOrderController = async (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation Failed",
      errors: errors.array(),
    });
  }
  try {
    const { cart, address, couponCode, userId } = req.body;
    const { subtotal, detailedItems, ids, couponData, finalAmount } = await calculateSubtotal(cart, couponCode, userId);

    const order = await Order.create({
      cartId: ids,
      cartItems: detailedItems,
      subtotal,
      coupon: couponData,
      finalAmount,
      address,
    });

    // Update coupon usage if coupon was applied
    if (couponCode && couponData.discountAmount > 0) {
      try {
        await Coupon.findOneAndUpdate(
          { code: couponCode.toUpperCase() },
          {
            $inc: { usedCount: 1 },
            $push: {
              usedBy: {
                userId: userId || `guest_${Date.now()}`,
                orderAmount: subtotal,
                discountApplied: couponData.discountAmount
              }
            }
          }
        );
      } catch (couponError) {
        console.error('Error updating coupon usage:', couponError);
        // Continue with order creation even if coupon update fails
      }
    }

    // Prepare email content for restaurant
    const itemsList = detailedItems
      .map(
        (item) =>
          `<li>${item.name} - Quantity: ${item.quantity} - Price: ₹${item.price}</li>`
      )
      .join("");

    // Prepare coupon information for email
    const couponInfo = couponData.discountAmount > 0
      ? `<p><strong>Coupon Applied:</strong> ${couponData.code} (${couponData.type === 'percentage' ? `${couponData.value}%` : `₹${couponData.value}`} off)</p>
         <p><strong>Discount Amount:</strong> ₹${couponData.discountAmount}</p>`
      : '';

    const emailHtml = `
      <h2>New Order Received</h2>
      <p><strong>Order ID:</strong> ${order._id}</p>
      <p><strong>Customer:</strong> ${address.full_name}</p>
      <p><strong>Contact:</strong> ${address.phone_number}</p>
      <p><strong>Address:</strong> ${address.landmark}, ${address.village_mohalla}, ${address.city} - ${address.pincode}</p>
      <h3>Order Details:</h3>
      <ul>${itemsList}</ul>
      <p><strong>Subtotal:</strong> ₹${subtotal}</p>
      ${couponInfo}
      <p><strong>Final Amount:</strong> ₹${finalAmount}</p>
    `;

    // Send email to restaurant
    await sendEmail({
      to: RESTAURANT_EMAIL,
      subject: `New Order Received - ${order._id}`,
      text: `You have a new order from ${address.full_name}, total ₹${finalAmount}.`,
      html: emailHtml,
    });

    res.status(201).json({
      success: true,
      message: "Order confirmed",
      orderId: order._id,
    });
  } catch (error) {
    next(error);
  }
};
