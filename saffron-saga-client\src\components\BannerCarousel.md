# Banner Carousel Component

## Overview
A visually appealing carousel banner for Saffron Saga restaurant featuring birthday celebration themes with warm festive colors (burgundy, gold, maroon) that match the restaurant's premium brand.

## Features
- **3 Themed Slides**: Birthday celebration, offers, and booking
- **Auto-slide**: Changes every 5 seconds
- **Interactive Navigation**: Arrow buttons and dot indicators
- **Mobile Responsive**: Optimized for all screen sizes
- **Animated Elements**: Floating balloons, confetti, and bouncing icons
- **Premium Styling**: Gradient backgrounds with gold accents

## Slides Content

### Slide 1: Birthday Celebration
- **Theme**: Celebrating birthday at the restaurant
- **Text**: "🎉 Celebrate Your Birthday at Saffron Saga"
- **Subtitle**: "Make your special day unforgettable with our premium dining experience"
- **CTA**: "BOOK NOW"
- **Background**: Burgundy to crimson to gold gradient

### Slide 2: Offers and Perks
- **Theme**: Special birthday offers
- **Text**: "🎂 Free Cake + Custom Decor"
- **Subtitle**: "Complimentary birthday cake and personalized decorations for your celebration"
- **CTA**: "GRAB THE OFFER"
- **Background**: Maroon to gold to burgundy gradient

### Slide 3: Booking
- **Theme**: Reservation system
- **Text**: "📅 Reserve Your Special Day"
- **Subtitle**: "Book your birthday celebration in advance and let us handle everything"
- **CTA**: "CHECK AVAILABILITY"
- **Background**: Brown to gold to saddle brown gradient

## Color Scheme
- **Primary Gold**: #FFD700
- **Secondary Orange**: #FFA500
- **Burgundy**: #8B0000
- **Crimson**: #DC143C
- **Maroon**: #800020
- **Dark Red**: #8B0000
- **Text**: White with gold highlights

## Usage
```jsx
import BannerCarousel from '../components/BannerCarousel';

function Home() {
  return (
    <div>
      <Navbar />
      <BannerCarousel />
      <div className="content">
        {/* Other content */}
      </div>
    </div>
  );
}
```

## Customization
To modify the slides, edit the `slides` array in `BannerCarousel.jsx`:

```jsx
const slides = [
  {
    id: 1,
    title: "Your Custom Title",
    subtitle: "Your custom subtitle",
    ctaText: "YOUR CTA",
    ctaAction: () => {/* Your action */},
    backgroundClass: "your-custom-class",
    icon: "🎉"
  }
];
```

## Responsive Breakpoints
- **Desktop**: 400px height, full features
- **Tablet** (≤768px): 300px height, stacked layout
- **Mobile** (≤480px): 250px height, hidden visuals

## Animation Details
- **Bounce**: Icons bounce every 2 seconds
- **Float**: Balloons float up and down over 3 seconds
- **Confetti Fall**: Confetti particles fall and rotate over 4 seconds
- **Slide Transition**: Smooth cubic-bezier transition over 0.6 seconds

## Dependencies
- React (hooks: useState, useEffect)
- Remix Icons (for navigation arrows)
- CSS3 animations and gradients

## File Structure
```
src/components/
├── BannerCarousel.jsx    # Main component
├── BannerCarousel.css    # Styling and animations
└── BannerCarousel.md     # Documentation
```

## Browser Support
- Modern browsers with CSS Grid and Flexbox support
- CSS3 animations and transforms
- ES6+ JavaScript features
