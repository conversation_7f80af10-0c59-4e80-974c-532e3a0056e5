// CORS Testing Utility

/**
 * Test CORS configuration with the server
 * @returns {Promise<Object>} Test results
 */
export const testCorsConnection = async () => {
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
  const results = {
    apiBaseUrl,
    timestamp: new Date().toISOString(),
    tests: {}
  };

  console.group('🔍 CORS Connection Test');
  console.log('Testing API Base URL:', apiBaseUrl);

  try {
    // Test 1: Simple GET request to health endpoint
    console.log('\n📡 Test 1: Health Check');
    try {
      const healthResponse = await fetch(`${apiBaseUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      results.tests.health = {
        success: healthResponse.ok,
        status: healthResponse.status,
        statusText: healthResponse.statusText,
        data: healthResponse.ok ? await healthResponse.json() : null
      };

      console.log(healthResponse.ok ? '✅ Health check passed' : '❌ Health check failed');
      console.log('Status:', healthResponse.status, healthResponse.statusText);
    } catch (error) {
      results.tests.health = {
        success: false,
        error: error.message
      };
      console.log('❌ Health check error:', error.message);
    }

    // Test 2: CORS-specific test endpoint
    console.log('\n🔒 Test 2: CORS Test Endpoint');
    try {
      const corsResponse = await fetch(`${apiBaseUrl}/cors-test`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Test credentials
      });

      results.tests.cors = {
        success: corsResponse.ok,
        status: corsResponse.status,
        statusText: corsResponse.statusText,
        data: corsResponse.ok ? await corsResponse.json() : null
      };

      console.log(corsResponse.ok ? '✅ CORS test passed' : '❌ CORS test failed');
      console.log('Status:', corsResponse.status, corsResponse.statusText);
      
      if (corsResponse.ok) {
        const data = await corsResponse.json();
        console.log('Server detected origin:', data.requestOrigin);
        console.log('Allowed origins:', data.allowedOrigins);
      }
    } catch (error) {
      results.tests.cors = {
        success: false,
        error: error.message
      };
      console.log('❌ CORS test error:', error.message);
    }

    // Test 3: POST request (like order creation)
    console.log('\n📝 Test 3: POST Request Test');
    try {
      const postResponse = await fetch(`${apiBaseUrl}/cors-test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ test: true })
      });

      results.tests.post = {
        success: postResponse.ok,
        status: postResponse.status,
        statusText: postResponse.statusText
      };

      console.log(postResponse.ok ? '✅ POST request passed' : '❌ POST request failed');
      console.log('Status:', postResponse.status, postResponse.statusText);
    } catch (error) {
      results.tests.post = {
        success: false,
        error: error.message
      };
      console.log('❌ POST request error:', error.message);
    }

    // Overall result
    const allTestsPassed = Object.values(results.tests).every(test => test.success);
    results.overall = allTestsPassed;

    console.log('\n🎯 Overall Result:', allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');

    if (!allTestsPassed) {
      console.log('\n💡 Troubleshooting Tips:');
      console.log('1. Check that your server is running and accessible');
      console.log('2. Verify CORS_ORIGIN in server environment includes your client domain');
      console.log('3. Ensure your client domain matches exactly (including protocol)');
      console.log('4. Check server logs for CORS-related messages');
    }

  } catch (error) {
    console.error('❌ CORS test suite failed:', error);
    results.error = error.message;
    results.overall = false;
  }

  console.groupEnd();
  return results;
};

/**
 * Get current client origin information
 * @returns {Object} Origin information
 */
export const getClientOriginInfo = () => {
  const info = {
    origin: window.location.origin,
    protocol: window.location.protocol,
    hostname: window.location.hostname,
    port: window.location.port,
    href: window.location.href,
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL
  };

  console.log('🌐 Client Origin Info:', info);
  return info;
};

/**
 * Check if current origin would be allowed by server CORS
 * @param {string} corsOriginString - Server CORS_ORIGIN configuration
 * @returns {boolean} Whether current origin would be allowed
 */
export const checkOriginAllowed = (corsOriginString) => {
  if (!corsOriginString) return false;

  const currentOrigin = window.location.origin;
  const allowedOrigins = corsOriginString.split(',').map(url => url.trim().replace(/\/$/, ''));

  const isAllowed = allowedOrigins.some(allowedOrigin => {
    // Exact match
    if (allowedOrigin === currentOrigin) return true;
    
    // Wildcard subdomain match
    if (allowedOrigin.startsWith('*.')) {
      const domain = allowedOrigin.substring(2);
      return currentOrigin.endsWith(domain);
    }
    
    return false;
  });

  console.log('🔍 Origin Check:');
  console.log('Current origin:', currentOrigin);
  console.log('Allowed origins:', allowedOrigins);
  console.log('Is allowed:', isAllowed);

  return isAllowed;
};

/**
 * Run CORS test and display results (for development)
 */
export const runCorsTest = async () => {
  console.log('🚀 Running CORS connection test...');
  
  // Show client info
  getClientOriginInfo();
  
  // Run tests
  const results = await testCorsConnection();
  
  return results;
};
