/* Banner Carousel Styles */
.banner-carousel {
  width: 100%;
  height: 400px;
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.carousel-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.slides-wrapper {
  display: flex;
  width: 300%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide {
  width: 33.333%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Slide Backgrounds */
.slide-birthday {
  background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B8860B 100%);
}

.slide-offer {
  background: linear-gradient(135deg, #800020 0%, #DAA520 50%, #8B0000 100%);
}

.slide-booking {
  background: linear-gradient(135deg, #722F37 0%, #B8860B 50%, #8B4513 100%);
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.slide-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 90%;
  max-width: 1200px;
  padding: 2rem;
}

.slide-text {
  flex: 1;
  color: white;
  max-width: 600px;
}

.slide-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  animation: bounce 2s infinite;
}

.slide-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  color: #FFD700;
}

.slide-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  line-height: 1.4;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.cta-button {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #8B0000;
  border: none;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
  background: linear-gradient(45deg, #FFA500, #FFD700);
}

.slide-visual {
  flex: 1;
  position: relative;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.visual-decoration {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Animated Balloons */
.balloon {
  position: absolute;
  width: 40px;
  height: 50px;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  animation: float 3s ease-in-out infinite;
}

.balloon-1 {
  background: #FFD700;
  top: 20%;
  right: 20%;
  animation-delay: 0s;
}

.balloon-2 {
  background: #DC143C;
  top: 40%;
  right: 40%;
  animation-delay: 1s;
}

.balloon-3 {
  background: #FF6347;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

/* Confetti */
.confetti {
  position: absolute;
  width: 10px;
  height: 10px;
  background: #FFD700;
  animation: confetti-fall 4s linear infinite;
}

.confetti-1 {
  left: 20%;
  animation-delay: 0s;
  background: #FFD700;
}

.confetti-2 {
  left: 50%;
  animation-delay: 1s;
  background: #DC143C;
}

.confetti-3 {
  left: 80%;
  animation-delay: 2s;
  background: #FF6347;
}

/* Navigation Arrows */
.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 215, 0, 0.9);
  color: #8B0000;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 3;
}

.nav-arrow:hover {
  background: #FFD700;
  transform: translateY(-50%) scale(1.1);
}

.nav-arrow-left {
  left: 20px;
}

.nav-arrow-right {
  right: 20px;
}

/* Dots Indicator */
.dots-container {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 3;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #FFD700;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: #FFD700;
  transform: scale(1.2);
}

.dot:hover {
  background: rgba(255, 215, 0, 0.7);
}

/* Logo */
.carousel-logo {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 3;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: #FFD700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  font-family: 'Georgia', serif;
}

/* Animations */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(400px) rotate(360deg);
    opacity: 0;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .banner-carousel {
    height: 300px;
  }

  .slide-content {
    flex-direction: column;
    text-align: center;
    padding: 1rem;
  }

  .slide-title {
    font-size: 1.8rem;
  }

  .slide-subtitle {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .cta-button {
    padding: 0.8rem 2rem;
    font-size: 1rem;
  }

  .slide-visual {
    height: 150px;
    margin-top: 1rem;
  }

  .nav-arrow {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .nav-arrow-left {
    left: 10px;
  }

  .nav-arrow-right {
    right: 10px;
  }
}

@media (max-width: 480px) {
  .banner-carousel {
    height: 250px;
    margin-bottom: 0.5rem;
  }

  .slide-title {
    font-size: 1.5rem;
  }

  .slide-subtitle {
    font-size: 0.9rem;
  }

  .slide-visual {
    display: none;
  }
}