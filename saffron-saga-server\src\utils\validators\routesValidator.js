export async function validateRoutes(app, requiredRoutes) {
  try {
    const registeredRoutes = app._router.stack
      .filter(r => r.route)
      .map(r => r.route.path);
    
    const missingRoutes = requiredRoutes.filter(
      route => !registeredRoutes.some(r => r.startsWith(route))
    );
    
    if (missingRoutes.length === 0) {
      return { success: true };
    }
    return { 
      success: false, 
      error: `Missing routes: ${missingRoutes.join(', ')}` 
    };
  } catch (error) {
    return { 
      success: false, 
      error: `Route validation failed: ${error.message}` 
    };
  }
}
