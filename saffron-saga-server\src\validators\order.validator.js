import { body } from "express-validator";
import mongoose from "mongoose";

export const orderValidationRules = [
  // Validate cart array
  body("cart")
    .isArray({ min: 1 })
    .withMessage("Cart must be a non-empty array"),

  body("cart.*._id")
    .notEmpty()
    .withMessage("Product ID is required")
    .custom((value) => mongoose.Types.ObjectId.isValid(value))
    .withMessage("Invalid product ID"),

  body("cart.*.quantity")
    .notEmpty()
    .withMessage("Quantity is required")
    .isInt({ min: 1 })
    .withMessage("Quantity must be at least 1"),

  // Address validations
  body("address.full_name")
    .notEmpty()
    .withMessage("Full name is required")
    .isString()
    .isLength({ min: 3 }),

  body("address.phone_number")
    .notEmpty()
    .withMessage("Phone number is required")
    .matches(/^[6-9]\d{9}$/)
    .withMessage("Phone number is invalid"),

  body("address.pincode")
    .notEmpty()
    .withMessage("Pincode is required")
    .isInt({ min: 100000, max: 999999 })
    .withMessage("Invalid pincode"),

  body("address.city")
    .notEmpty()
    .withMessage("City is required")
    .isString()
    .isLength({ min: 2 }),

  body("address.landmark")
    .notEmpty()
    .withMessage("Landmark is required")
    .isString()
    .isLength({ min: 3 }),

  body("address.village_mohalla")
    .notEmpty()
    .withMessage("Village/Mohalla is required")
    .isString()
    .isLength({ min: 2 }),
];
