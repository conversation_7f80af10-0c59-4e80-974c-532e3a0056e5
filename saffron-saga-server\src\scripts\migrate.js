#!/usr/bin/env node

/**
 * Database Migration Script
 * Handles data migrations and schema updates
 */

import mongoose from 'mongoose';
import { MONGODB_URI, DB_NAME } from '../config/env.js';

// Import models
import { Admin } from '../models/admin.model.js';
import { MenuItem } from '../models/menuItem.model.js';
import { Order } from '../models/order.model.js';

/**
 * Run database migrations
 */
async function runMigrations() {
  try {
    console.log('🔄 Starting database migrations...');
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      dbName: DB_NAME,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
    });
    
    console.log('✅ Connected to MongoDB for migrations');
    
    // Run migration functions
    await migration_001_addDefaultImages();
    await migration_002_updateOrderStatus();
    await migration_003_normalizeData();
    
    console.log('🎉 All migrations completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    throw error;
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Migration connection closed');
  }
}

/**
 * Migration 001: Add default images to menu items without images
 */
async function migration_001_addDefaultImages() {
  try {
    console.log('📝 Running migration 001: Add default images...');
    
    const result = await MenuItem.updateMany(
      { 
        $or: [
          { image: { $exists: false } },
          { image: null },
          { image: '' }
        ]
      },
      { 
        $set: { 
          image: '/assets/default-image.png' 
        }
      }
    );
    
    console.log(`  ✅ Updated ${result.modifiedCount} menu items with default images`);
    
  } catch (error) {
    console.warn('  ⚠️  Migration 001 warning:', error.message);
  }
}

/**
 * Migration 002: Ensure all orders have proper status
 */
async function migration_002_updateOrderStatus() {
  try {
    console.log('📝 Running migration 002: Update order status...');
    
    const result = await Order.updateMany(
      { 
        $or: [
          { status: { $exists: false } },
          { status: null },
          { status: '' }
        ]
      },
      { 
        $set: { 
          status: 'pending' 
        }
      }
    );
    
    console.log(`  ✅ Updated ${result.modifiedCount} orders with default status`);
    
  } catch (error) {
    console.warn('  ⚠️  Migration 002 warning:', error.message);
  }
}

/**
 * Migration 003: Normalize data formats
 */
async function migration_003_normalizeData() {
  try {
    console.log('📝 Running migration 003: Normalize data formats...');
    
    // Ensure all admin emails are lowercase
    const adminResult = await Admin.updateMany(
      {},
      [
        {
          $set: {
            email: { $toLower: '$email' }
          }
        }
      ]
    );
    
    // Ensure all menu item names are lowercase
    const menuResult = await MenuItem.updateMany(
      {},
      [
        {
          $set: {
            name: { $toLower: '$name' },
            description: { $toLower: '$description' },
            category: { $toLower: '$category' }
          }
        }
      ]
    );
    
    console.log(`  ✅ Normalized ${adminResult.modifiedCount} admin records`);
    console.log(`  ✅ Normalized ${menuResult.modifiedCount} menu item records`);
    
  } catch (error) {
    console.warn('  ⚠️  Migration 003 warning:', error.message);
  }
}

/**
 * Handle script execution
 */
async function main() {
  try {
    await runMigrations();
    process.exit(0);
  } catch (error) {
    console.error('💥 Migration script failed:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { runMigrations };
