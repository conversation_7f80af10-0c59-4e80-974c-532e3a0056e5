import { MenuItem } from "../models/menuItem.model.js";
import { Coupon } from "../models/coupon.model.js";

export const calculateSubtotal = async (cart, couponCode = null, userId = null) => {
  // Step 1: Extract IDs
  const ids = cart.map((item) => item._id);

  // Step 2: Fetch menu items with prices
  const menuItems = await MenuItem.find({ _id: { $in: ids } });

  // Step 3: Map quantity by ID
  const quantityMap = Object.fromEntries(
    cart.map((item) => [item._id.toString(), item.quantity])
  );

  // Step 4: Calculate subtotal
  let subtotal = 0;
  const detailedItems = [];

  for (const item of menuItems) {
    const id = item._id.toString();
    const quantity = quantityMap[id] || 0;
    const price = item.price;
    subtotal += price * quantity;

    // Optional: collect item detail
    detailedItems.push({
      _id: id,
      name: item.name,
      price,
      quantity,
      total: price * quantity,
    });
  }

  // Initialize coupon data
  let couponData = {
    code: null,
    discountAmount: 0,
    type: null,
    value: null
  };
  let finalAmount = subtotal;

  // Apply coupon if provided
  if (couponCode) {
    try {
      const coupon = await Coupon.findValidCoupon(couponCode);

      if (coupon) {
        // Check usage limits
        const canUse = !coupon.usageLimit || coupon.usedCount < coupon.usageLimit;
        const userCanUse = !userId || coupon.canUserUse(userId);

        if (canUse && userCanUse && subtotal >= coupon.minimumOrderAmount) {
          const discountAmount = coupon.calculateDiscount(subtotal);

          couponData = {
            code: coupon.code,
            discountAmount,
            type: coupon.type,
            value: coupon.value
          };

          finalAmount = subtotal - discountAmount;
        }
      }
    } catch (error) {
      console.error('Error applying coupon:', error);
      // Continue without coupon if there's an error
    }
  }

  return {
    subtotal,
    detailedItems,
    ids,
    couponData,
    finalAmount
  };
};
