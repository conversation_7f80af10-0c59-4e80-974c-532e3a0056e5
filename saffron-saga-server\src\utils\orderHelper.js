import { MenuItem } from "../models/menuItem.model.js";

export const calculateSubtotal = async (cart) => {
  // Step 1: Extract IDs
  const ids = cart.map((item) => item._id);

  // Step 2: Fetch menu items with prices
  const menuItems = await MenuItem.find({ _id: { $in: ids } });

  // Step 3: Map quantity by ID
  const quantityMap = Object.fromEntries(
    cart.map((item) => [item._id.toString(), item.quantity])
  );

  // Step 4: Calculate subtotal
  let subtotal = 0;
  const detailedItems = [];

  for (const item of menuItems) {
    const id = item._id.toString();
    const quantity = quantityMap[id] || 0;
    const price = item.price;
    subtotal += price * quantity;

    // Optional: collect item detail
    detailedItems.push({
      _id: id,
      name: item.name,
      price,
      quantity,
      total: price * quantity,
    });
  }

  return { subtotal, detailedItems, ids };
};
